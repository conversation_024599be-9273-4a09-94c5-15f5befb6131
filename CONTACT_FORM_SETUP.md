# Contact Form Setup Guide

This guide explains how to set up the contact form functionality in your Next.js application.

## 1. Environment Configuration

Create a `.env.local` file in your project root with the following variables:

```env
# Contact Form Email Configuration
CONTACT_EMAIL=<EMAIL>
CONTACT_SUBJECT=Contact Inquiry from Technoloway Website

# Choose your email service (sendgrid, mailgun, or nodemailer)
EMAIL_SERVICE=sendgrid

# SendGrid Configuration (if using SendGrid)
SENDGRID_API_KEY=your_sendgrid_api_key_here

# Mailgun Configuration (if using Mailgun)
MAILGUN_API_KEY=your_mailgun_api_key_here
MAILGUN_DOMAIN=your_mailgun_domain_here

# SMTP Configuration (if using Nodemailer)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

## 2. Email Service Setup

### Option A: SendGrid (Recommended)
1. Sign up for a SendGrid account
2. Create an API key
3. Add the API key to your `.env.local` file
4. Set `EMAIL_SERVICE=sendgrid`

### Option B: Mailgun
1. Sign up for a Mailgun account
2. Get your API key and domain
3. Add them to your `.env.local` file
4. Set `EMAIL_SERVICE=mailgun`

### Option C: Nodemailer (SMTP)
1. Install nodemailer: `npm install nodemailer`
2. Configure your SMTP settings in `.env.local`
3. Set `EMAIL_SERVICE=nodemailer`

## 3. API Route

The contact form uses the API route at `/api/contact` which:
- Validates form data
- Sends emails using your configured service
- Returns success/error responses

## 4. Form Features

The contact form includes:
- Real-time validation
- Loading states
- Success/error messages
- Form reset on successful submission
- Responsive design

## 5. Testing

To test the contact form:
1. Fill out the form on the contact page
2. Submit the form
3. Check your email inbox
4. Check the console for any errors

## 6. Customization

You can customize:
- Email templates in `/src/lib/email.ts`
- Form validation in `/src/app/api/contact/route.ts`
- Form styling in the contact page component
- Success/error messages

## 7. Security Notes

- Never commit your `.env.local` file to version control
- Use environment variables for all sensitive data
- Consider adding rate limiting for production
- Validate all form inputs on both client and server side

## 8. Production Deployment

For production:
1. Set up your email service credentials
2. Configure environment variables on your hosting platform
3. Test the form thoroughly
4. Consider adding CAPTCHA or other spam prevention
5. Monitor email delivery rates
