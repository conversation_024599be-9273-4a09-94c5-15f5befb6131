'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import ScrollingTicker from '@/components/layout/ScrollingTicker';


export default function About() {
  const [openPotentialAccordion, setOpenPotentialAccordion] = useState<string | null>('pcollapse1');

  const handlePotentialAccordionToggle = (accordionId: string) => {
    setOpenPotentialAccordion(openPotentialAccordion === accordionId ? null : accordionId);
  };

  useEffect(() => {
    // Hide preloader immediately
    const preloader = document.querySelector('.preloader') as HTMLElement;
    if (preloader) {
      preloader.style.display = 'none';
    }
  }, []);

  return (
    <>
      {/* Preloader Start */}
      <div className="preloader" style={{ display: 'none' }}>
        <div className="loading-container">
          <div className="loading" />
          <div id="loading-icon">
            <Image src="/images/loader.svg" alt="Loading" width={50} height={50} />
          </div>
        </div>
      </div>
      {/* Preloader End */}



      <PageHeader 
        title="About"
        titleHighlight="us"
        breadcrumbs={[
          { label: 'home', href: '/' },
          { label: 'about us', isActive: true }
        ]}
      />

      <ScrollingTicker />

      {/* About Us Section Start */}
      <div className="about-us page-about-us">
        <div className="container">
          <div className="row section-row align-items-center">
            <div className="col-lg-12">
              {/* Section Title Start */}
              <div className="section-title section-title-center">
                <h3 className="wow fadeInUp">About Us</h3>
                <h2
                  className="wow fadeInUp"
                  data-wow-delay="0.2s"
                  data-cursor="-opaque"
                >
                  We provide innovative solutions to help your cybersecurity startup
                  grow, building a trusted <span>brand with precision</span>
                </h2>
              </div>
              {/* Section Title End */}
            </div>
          </div>
          <div className="row">
            <div className="col-lg-4">
              {/* About Counter Box Start */}
              <div className="about-counter-box">
                <h2>
                  <span className="counter">26</span>
                </h2>
                <p>Years of work experience</p>
              </div>
              {/* About Counter Box End */}
            </div>
            <div className="col-lg-8">
              {/* About Us Content Start */}
              <div className="about-us-content">
                {/* About Us List Start */}
                <div className="about-us-list">
                  {/* About List Item Start */}
                  <div className="about-list-item wow fadeInUp">
                    <Image src="/images/icon-about-item-1.svg" alt="Mission" width={60} height={60} />
                    <h3>Our mission</h3>
                    <p>
                      At the core of our mission is the belief that every business
                      deserves a customized cybersecurity.
                    </p>
                  </div>
                  {/* About List Item End */}
                  {/* About List Item Start */}
                  <div
                    className="about-list-item wow fadeInUp"
                    data-wow-delay="0.2s"
                  >
                    <Image src="/images/icon-about-item-2.svg" alt="Vision" width={60} height={60} />
                    <h3>Our vision</h3>
                    <p>
                      Our vision is to redefine the cybersecurity with innovative,
                      adaptive solutions that evolve.
                    </p>
                  </div>
                  {/* About List Item End */}
                </div>
                {/* About Us List End */}
                {/* About Us Body Start */}
                <div className="about-us-body wow fadeInUp" data-wow-delay="0.4s">
                  <p>
                    We specialize in empowering cybersecurity startups with
                    tailored, innovative solutions designed to foster growth and
                    establish a trusted brand identity. From building a strong
                    online presence to delivering cutting-edge strategies, we
                    combine precision, expertise, and creativity.
                  </p>
                </div>
                {/* About Us Body End */}
              </div>
              {/* About Us Content End */}
            </div>
          </div>
        </div>
      </div>
      {/* About Us Section End */}

      {/* Our Potential Section Start */}
  <div className="our-potential">
    <div className="container">
      <div className="row align-items-center">
        <div className="col-lg-7">
          {/* Our FAQs Content Start */}
          <div className="our-potential-content">
            {/* Section Title Start */}
            <div className="section-title">
              <h3 className="wow fadeInUp">Unlock your business potential</h3>
              <h2
                className="wow fadeInUp"
                data-wow-delay="0.2s"
                data-cursor="-opaque"
              >
                Developing cybersecurity solutions to propel your{" "}
                <span>business forward</span>
              </h2>
            </div>
            {/* Section Title End */}
            {/* FAQ Accordion Start */}
            <div className="potential-accordion" id="accordion">
              {/* FAQ Item Start */}
              <div
                className="accordion-item wow fadeInUp"
                data-wow-delay="0.4s"
              >
                <h2 className="accordion-header" id="pheading1">
                  <button
                    className={`accordion-button ${openPotentialAccordion === 'pcollapse1' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handlePotentialAccordionToggle('pcollapse1')}
                    aria-expanded={openPotentialAccordion === 'pcollapse1'}
                    aria-controls="pcollapse1"
                  >
                    Learn Our Company Mission
                  </button>
                </h2>
                <div
                  id="pcollapse1"
                  className={`accordion-collapse ${openPotentialAccordion === 'pcollapse1' ? 'show' : 'collapse'}`}
                  aria-labelledby="pheading1"
                >
                  <div className="accordion-body">
                    <div className="accordion-image">
                      <figure>
                        <img src="/images/potential-accordion-img.jpg" alt="" />
                      </figure>
                    </div>
                    <div className="accordion-item-content">
                      <p>
                        We strive to create a safer digital environment where
                        businesses can thrive and individuals can feel secure.
                      </p>
                      <ul>
                        <li>Enabling Secure Growth</li>
                        <li>Protecting What Matters Most</li>
                        <li>Driving Innovation in Cybersecurity</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
              {/* FAQ Item Start */}
              <div
                className="accordion-item wow fadeInUp"
                data-wow-delay="0.6s"
              >
                <h2 className="accordion-header" id="pheading2">
                  <button
                    className={`accordion-button ${openPotentialAccordion === 'pcollapse2' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handlePotentialAccordionToggle('pcollapse2')}
                    aria-expanded={openPotentialAccordion === 'pcollapse2'}
                    aria-controls="pcollapse2"
                  >
                    Our Company Vision
                  </button>
                </h2>
                <div
                  id="pcollapse2"
                  className={`accordion-collapse ${openPotentialAccordion === 'pcollapse2' ? 'show' : 'collapse'}`}
                  aria-labelledby="pheading2"
                >
                  <div className="accordion-body">
                    <div className="accordion-image">
                      <figure>
                        <img src="/images/potential-accordion-img.jpg" alt="" />
                      </figure>
                    </div>
                    <div className="accordion-item-content">
                      <p>
                        We strive to create a safer digital environment where
                        businesses can thrive and individuals can feel secure.
                      </p>
                      <ul>
                        <li>Enabling Secure Growth</li>
                        <li>Protecting What Matters Most</li>
                        <li>Driving Innovation in Cybersecurity</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
              {/* FAQ Item Start */}
              <div
                className="accordion-item wow fadeInUp"
                data-wow-delay="0.8s"
              >
                <h2 className="accordion-header" id="pheading3">
                  <button
                    className={`accordion-button ${openPotentialAccordion === 'pcollapse3' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handlePotentialAccordionToggle('pcollapse3')}
                    aria-expanded={openPotentialAccordion === 'pcollapse3'}
                    aria-controls="pcollapse3"
                  >
                    Our Philosophy
                  </button>
                </h2>
                <div
                  id="pcollapse3"
                  className={`accordion-collapse ${openPotentialAccordion === 'pcollapse3' ? 'show' : 'collapse'}`}
                  aria-labelledby="pheading3"
                >
                  <div className="accordion-body">
                    <div className="accordion-image">
                      <figure>
                        <img src="/images/potential-accordion-img.jpg" alt="" />
                      </figure>
                    </div>
                    <div className="accordion-item-content">
                      <p>
                        We strive to create a safer digital environment where
                        businesses can thrive and individuals can feel secure.
                      </p>
                      <ul>
                        <li>Enabling Secure Growth</li>
                        <li>Protecting What Matters Most</li>
                        <li>Driving Innovation in Cybersecurity</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
              {/* FAQ Item Start */}
              <div className="accordion-item wow fadeInUp" data-wow-delay="1s">
                <h2 className="accordion-header" id="pheading4">
                  <button
                    className={`accordion-button ${openPotentialAccordion === 'pcollapse4' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handlePotentialAccordionToggle('pcollapse4')}
                    aria-expanded={openPotentialAccordion === 'pcollapse4'}
                    aria-controls="pcollapse4"
                  >
                    Our Strategy
                  </button>
                </h2>
                <div
                  id="pcollapse4"
                  className={`accordion-collapse ${openPotentialAccordion === 'pcollapse4' ? 'show' : 'collapse'}`}
                  aria-labelledby="pheading4"
                >
                  <div className="accordion-body">
                    <div className="accordion-image">
                      <figure>
                        <img src="/images/potential-accordion-img.jpg" alt="" />
                      </figure>
                    </div>
                    <div className="accordion-item-content">
                      <p>
                        We strive to create a safer digital environment where
                        businesses can thrive and individuals can feel secure.
                      </p>
                      <ul>
                        <li>Enabling Secure Growth</li>
                        <li>Protecting What Matters Most</li>
                        <li>Driving Innovation in Cybersecurity</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
            </div>
            {/* FAQ Accordion End */}
          </div>
        </div>
        <div className="col-lg-5">
          {/* Potential Image Start */}
          <div className="potential-image">
            <figure className="image-anime reveal">
              <img src="/images/potential-image.jpg" alt="" />
            </figure>
          </div>
          {/* Potential Image End */}
        </div>
      </div>
    </div>
  </div>
  {/* Our Potential Section End */}

  {/* Who We Are Section Start */}
  <div className="who-we-are">
    <div className="container">
      <div className="row">
        <div className="col-lg-6">
          {/* Who We Are Images Start */}
          <div className="who-we-are-images">
            {/* Who We image Box Start */}
            <div className="who-we-img-box">
              {/* Customer Review Box Start */}
              <div className="customer-review-box">
                {/* Customer Review Images Start */}
                <div className="customer-review-images">
                  {/* Customer Image Start */}
                  <div className="customer-image reveal">
                    <figure className="image-anime">
                      <img src="/images/author-1.jpg" alt="" />
                    </figure>
                  </div>
                  {/* Customer Image End */}
                  {/* Customer Image Start */}
                  <div className="customer-image reveal">
                    <figure className="image-anime">
                      <img src="/images/author-2.jpg" alt="" />
                    </figure>
                  </div>
                  {/* Customer Image End */}
                  {/* Customer Image Start */}
                  <div className="customer-image reveal">
                    <figure className="image-anime">
                      <img src="/images/author-3.jpg" alt="" />
                    </figure>
                  </div>
                  {/* Customer Image End */}
                </div>
                {/* Customer Review Images End */}
                {/* Customer Review Box Content Start */}
                <div className="customer-review-content">
                  <p>
                    <span className="counter">80</span>% fast incident response
                  </p>
                </div>
                {/* Customer Review Box Content End */}
              </div>
              {/* Customer Review Box End */}
              {/* Who We Are Image Start */}
              <div className="who-we-are-img">
                <figure className="image-anime reveal">
                  <img src="/images/who-we-are-img-1.jpg" alt="" />
                </figure>
              </div>
              {/* Who We Are Image End */}
            </div>
            {/* Who We image Box End */}
            {/* Who We image Box Start */}
            <div className="who-we-img-box">
              <div className="who-we-are-img">
                <figure className="image-anime reveal">
                  <img src="/images/who-we-are-img-2.jpg" alt="" />
                </figure>
              </div>
            </div>
            {/* Who We image Box End */}
            {/* Readmore Image Crcle Start */}
            <div className="readmore-img-circle">
              <a href="contact.html">
                <img src="/images/readmore-img-circle.svg" alt="" />
              </a>
            </div>
            {/* Readmore Image Crcle End */}
          </div>
          {/* Who We Are Images End */}
        </div>
        <div className="col-lg-6">
          {/* Who We Are Counter Start */}
          <div className="who-we-are-content">
            {/* Section Title Start */}
            <div className="section-title">
              <h3 className="wow fadeInUp">who we are</h3>
              <h2
                className="wow fadeInUp"
                data-wow-delay="0.2s"
                data-cursor="-opaque"
              >
                Insights into the growing importance{" "}
                <span>of cybersecurity</span>
              </h2>
              <p className="wow fadeInUp" data-wow-delay="0.4s">
                In a world where cyberattacks are becoming more sophisticated,
                your business deserves the best protection. Our expert team
                leverages cutting-edge technology.
              </p>
            </div>
            {/* Section Title End */}
            {/* Who We Are Counters Start */}
            <div className="who-we-are-counters">
              {/* Who We Counters Item Start */}
              <div className="who-we-counter-item">
                <h2>
                  <span className="counter">99.9</span>%
                </h2>
                <p>
                  Our advanced solution ensure nearly perfect protection against
                  malware, ransomware, other cyber attacks.
                </p>
              </div>
              {/* Who We Counters Item End */}
              {/* Who We Counters Item Start */}
              <div className="who-we-counter-item">
                <h2>
                  <span className="counter">500</span>+
                </h2>
                <p>
                  Over the year, we've secured more than 500 businesses across
                  various industries, ensuring their data.
                </p>
              </div>
              {/* Who We Counters Item End */}
            </div>
            {/* Who We Are Counters End */}
          </div>
          {/* Who We Are Counter End */}
        </div>
      </div>
    </div>
  </div>
  {/* Who We Are Section End */}
  {/* Our Specialties Section Start */}
  <div className="our-specialties">
    <div className="container">
      <div className="row align-items-center">
        <div className="col-lg-6">
          {/* Our Specialties Content Start */}
          <div className="our-specialties-content">
            {/* Section Title Start */}
            <div className="section-title">
              <h3 className="wow fadeInUp">our specialties</h3>
              <h2
                className="wow fadeInUp"
                data-wow-delay="0.2s"
                data-cursor="-opaque"
              >
                Trusted experts committed to securing <span>your digital</span>
              </h2>
              <p className="wow fadeInUp" data-wow-delay="0.4s">
                In a world where cyberattacks are becoming more sophisticated,
                your business deserves the best protection. Our expert team
                leverages cutting-edge technology.
              </p>
            </div>
            {/* Section Title End */}
            {/* Our Specialties Button Start */}
            <div
              className="our-specialties-btn wow fadeInUp"
              data-wow-delay="0.6s"
            >
              <a href="contact.html" className="btn-default">
                contact now
              </a>
            </div>
            {/* Our Specialties Button End */}
          </div>
          {/* Our Specialties Content End */}
        </div>
        <div className="col-lg-6">
          {/* Specialties Item Box Start */}
          <div className="specialties-item-box">
            {/* Specialties Item Start */}
            <div className="specialties-item wow fadeInUp">
              <div className="icon-box">
                <img src="/images/icon-specialties-item-1.svg" alt="" />
              </div>
              <div className="specialties-item-content">
                <h3>24/7 Threat Monitoring</h3>
                <p>
                  Our dedicated team ensures round-the-clock monitoring to
                  detect.
                </p>
              </div>
            </div>
            {/* Specialties Item End */}
            {/* Specialties Item Start */}
            <div
              className="specialties-item wow fadeInUp"
              data-wow-delay="0.2s"
            >
              <div className="icon-box">
                <img src="/images/icon-specialties-item-2.svg" alt="" />
              </div>
              <div className="specialties-item-content">
                <h3>End-to-End Security</h3>
                <p>From initial risk assessments to ongoing protection.</p>
              </div>
            </div>
            {/* Specialties Item End */}
            {/* Specialties Item Start */}
            <div
              className="specialties-item wow fadeInUp"
              data-wow-delay="0.4s"
            >
              <div className="icon-box">
                <img src="/images/icon-specialties-item-3.svg" alt="" />
              </div>
              <div className="specialties-item-content">
                <h3>Cutting-Edge Technology</h3>
                <p>
                  We utilize the latest tools and AI-driven systems to stay.
                </p>
              </div>
            </div>
            {/* Specialties Item End */}
            {/* Specialties Item Start */}
            <div
              className="specialties-item wow fadeInUp"
              data-wow-delay="0.6s"
            >
              <div className="icon-box">
                <img src="/images/icon-specialties-item-4.svg" alt="" />
              </div>
              <div className="specialties-item-content">
                <h3>Expertise You Can Trust</h3>
                <p>
                  Our team of certified experts delivers industry-leading
                  solutions.
                </p>
              </div>
            </div>
            {/* Specialties Item End */}
          </div>
        </div>
        <div className="col-lg-12">
          {/* Intro Video Box Start */}
          <div className="intro-video-box">
            <div className="intro-bg-image">
              <figure className="image-anime reveal">
                <img src="/images/intro-bg-image.jpg" alt="" />
              </figure>
            </div>
            {/* Video Play Button Start */}
            <div className="video-play-button">
              <a
                href="https://www.youtube.com/watch?v=Y-x0efG1seA"
                className="popup-video"
                data-cursor-text="Play"
              >
                <i className="fa-solid fa-play" />
              </a>
            </div>
            {/* Video Play Button End */}
          </div>
          {/* Intro Video Box End */}
        </div>
      </div>
    </div>
  </div>
  {/* Our Specialties Section End */}
  {/* Our Team Section Start */}
  <div className="our-team">
    <div className="container">
      <div className="row section-row align-items-center">
        <div className="col-lg-6">
          {/* Section Title Start */}
          <div className="section-title">
            <h3 className="wow fadeInUp">our team</h3>
            <h2
              className="wow fadeInUp"
              data-wow-delay="0.2s"
              data-cursor="-opaque"
            >
              Our team turning challenges into <span>secure solutions</span>
            </h2>
          </div>
          {/* Section Title End */}
        </div>
        <div className="col-lg-6">
          {/* Section Button Start */}
          <div className="section-btn wow fadeInUp" data-wow-delay="0.4s">
            <a href="team.html" className="btn-default">
              view all member
            </a>
          </div>
          {/* Section Button End */}
        </div>
      </div>
      <div className="row">
        <div className="col-lg-3 col-md-6">
          {/* Team Member Item Start */}
          <div className="team-item wow fadeInUp">
            {/* team Image Start */}
            <div className="team-image">
              <a href="team-single.html" className="image-anime">
                <figure>
                  <img src="/images/team-1.jpg" alt="" />
                </figure>
              </a>
            </div>
            {/* team Image End */}
            {/* Team Body Start */}
            <div className="team-body">
              {/* Team Content Start */}
              <div className="team-content">
                <h3>
                  <a href="team-single.html">Darrell steward</a>
                </h3>
                <p>Hacking specialist</p>
              </div>
              {/* Team Content End */}
              {/* Team Social List Start */}
              <div className="team-social-list">
                <ul>
                  <li>
                    <a href="#">
                      <i className="fa-brands fa-facebook-f" />
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i className="fa-brands fa-instagram" />
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i className="fa-brands fa-pinterest-p" />
                    </a>
                  </li>
                </ul>
              </div>
              {/* Team Social List End */}
            </div>
            {/* Team Body End */}
          </div>
          {/* Team Member Item End */}
        </div>
        <div className="col-lg-3 col-md-6">
          {/* Team Member Item Start */}
          <div className="team-item wow fadeInUp" data-wow-delay="0.2s">
            {/* team Image Start */}
            <div className="team-image">
              <a href="team-single.html" className="image-anime">
                <figure>
                  <img src="/images/team-2.jpg" alt="" />
                </figure>
              </a>
            </div>
            {/* team Image End */}
            {/* Team Body Start */}
            <div className="team-body">
              {/* Team Content Start */}
              <div className="team-content">
                <h3>
                  <a href="team-single.html">Albert flores</a>
                </h3>
                <p>Attack specialist</p>
              </div>
              {/* Team Content End */}
              {/* Team Social List Start */}
              <div className="team-social-list">
                <ul>
                  <li>
                    <a href="#">
                      <i className="fa-brands fa-facebook-f" />
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i className="fa-brands fa-instagram" />
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i className="fa-brands fa-pinterest-p" />
                    </a>
                  </li>
                </ul>
              </div>
              {/* Team Social List End */}
            </div>
            {/* Team Body End */}
          </div>
          {/* Team Member Item End */}
        </div>
        <div className="col-lg-3 col-md-6">
          {/* Team Member Item Start */}
          <div className="team-item wow fadeInUp" data-wow-delay="0.4s">
            {/* team Image Start */}
            <div className="team-image">
              <a href="team-single.html" className="image-anime">
                <figure>
                  <img src="/images/team-3.jpg" alt="" />
                </figure>
              </a>
            </div>
            {/* team Image End */}
            {/* Team Body Start */}
            <div className="team-body">
              {/* Team Content Start */}
              <div className="team-content">
                <h3>
                  <a href="team-single.html">Jacob jones</a>
                </h3>
                <p>Operation manager</p>
              </div>
              {/* Team Content End */}
              {/* Team Social List Start */}
              <div className="team-social-list">
                <ul>
                  <li>
                    <a href="#">
                      <i className="fa-brands fa-facebook-f" />
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i className="fa-brands fa-instagram" />
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i className="fa-brands fa-pinterest-p" />
                    </a>
                  </li>
                </ul>
              </div>
              {/* Team Social List End */}
            </div>
            {/* Team Body End */}
          </div>
          {/* Team Member Item End */}
        </div>
        <div className="col-lg-3 col-md-6">
          {/* Team Member Item Start */}
          <div className="team-item wow fadeInUp" data-wow-delay="0.6s">
            {/* team Image Start */}
            <div className="team-image">
              <a href="team-single.html" className="image-anime">
                <figure>
                  <img src="/images/team-4.jpg" alt="" />
                </figure>
              </a>
            </div>
            {/* team Image End */}
            {/* Team Body Start */}
            <div className="team-body">
              {/* Team Content Start */}
              <div className="team-content">
                <h3>
                  <a href="team-single.html">Ronald richards</a>
                </h3>
                <p>Hacking specialist</p>
              </div>
              {/* Team Content End */}
              {/* Team Social List Start */}
              <div className="team-social-list">
                <ul>
                  <li>
                    <a href="#">
                      <i className="fa-brands fa-facebook-f" />
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i className="fa-brands fa-instagram" />
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i className="fa-brands fa-pinterest-p" />
                    </a>
                  </li>
                </ul>
              </div>
              {/* Team Social List End */}
            </div>
            {/* Team Body End */}
          </div>
          {/* Team Member Item End */}
        </div>
      </div>
    </div>
  </div>
  {/* Our Team Section End */}
  {/* Our Success Section Start */}
  <div className="our-success">
    <div className="container">
      <div className="row align-items-center">
        <div className="col-lg-6">
          {/* Our Success Content Start */}
          <div className="our-success-content">
            {/* Section Title Start */}
            <div className="section-title">
              <h3 className="wow fadeInUp">Our Core values</h3>
              <h2
                className="wow fadeInUp"
                data-wow-delay="0.2s"
                data-cursor="-opaque"
              >
                Our core values the pillars of <span>our success</span>
              </h2>
              <p className="wow fadeInUp" data-wow-delay="0.4s">
                In a world where cyberattacks are becoming more sophisticated,
                your business deserves the best protection. Our expert team
                leverages cutting-edge technology.
              </p>
            </div>
            {/* Section Title End */}
            {/* Success Item List Start */}
            <div className="success-item-list">
              {/* Success Item Start */}
              <div className="success-item wow fadeInUp" data-wow-delay="0.6s">
                <div className="icon-box">
                  <img src="/images/icon-success-item-1.svg" alt="" />
                </div>
                <div className="success-item-content">
                  <h3>Integrity and Transparency</h3>
                  <p>
                    We believe in honesty and openness, ensuring our clients
                    receive reliable and trustworthy solutions.
                  </p>
                </div>
              </div>
              {/* Success Item End */}
              {/* Success Item Start */}
              <div className="success-item wow fadeInUp" data-wow-delay="0.8s">
                <div className="icon-box">
                  <img src="/images/icon-success-item-2.svg" alt="" />
                </div>
                <div className="success-item-content">
                  <h3>Innovation at the Core</h3>
                  <p>
                    By embracing the latest technologies and trends, we stay
                    ahead of emerging threats to deliver.
                  </p>
                </div>
              </div>
              {/* Success Item End */}
              {/* Success Item Start */}
              <div className="success-item wow fadeInUp" data-wow-delay="1s">
                <div className="icon-box">
                  <img src="/images/icon-success-item-3.svg" alt="" />
                </div>
                <div className="success-item-content">
                  <h3>Commitment to Excellence</h3>
                  <p>
                    Our dedication to delivering superior results drives us to
                    exceed expectations and set new standards.
                  </p>
                </div>
              </div>
              {/* Success Item End */}
            </div>
            {/* Success Item List End */}
          </div>
          {/* Our Success Content End */}
        </div>
        <div className="col-lg-6">
          {/* Our Success Images Start */}
          <div className="our-success-iamges">
            {/* Success image 1 Start */}
            <div className="success-img-1">
              <figure className="image-anime reveal">
                <img src="/images/success-img-1.jpg" alt="" />
              </figure>
            </div>
            {/* Success image 1 End */}
            {/* Success image 2 Start */}
            <div className="success-img-2">
              <figure className="image-anime reveal">
                <img src="/images/success-img-2.jpg" alt="" />
              </figure>
            </div>
            {/* Success image 2 End */}
          </div>
          {/* Our Success Images End */}
        </div>
      </div>
    </div>
  </div>
  {/* Our Success Section End */}
  {/* Our Partners Section Start */}
  <div className="our-partners">
    <div className="container">
      <div className="row">
        <div className="col-lg-12">
          {/* Our Partners Box Start */}
          <div className="our-partners-box">
            {/* Our Partners Content Start */}
            <div className="our-partners-content">
              {/* Section Title Start */}
              <div className="section-title">
                <h3 className="wow fadeInUp">Our partners</h3>
                <h2
                  className="wow fadeInUp"
                  data-wow-delay="0.2s"
                  data-cursor="-opaque"
                >
                  Our partners working together to defend the{" "}
                  <span>digital world</span>
                </h2>
              </div>
              {/* Section Title End */}
            </div>
            {/* Our Partners Content End */}
            {/* Our Partners Slider Start */}
            <div className="our-partners-slider">
              <div className="swiper">
                <div className="swiper-wrapper">
                  {/* Company Logo Start */}
                  <div className="swiper-slide">
                    <div className="partner-logo">
                      <img src="/images/company-logo-1.svg" alt="" />
                    </div>
                  </div>
                  {/* Company Logo End */}
                  {/* Company Logo Start */}
                  <div className="swiper-slide">
                    <div className="partner-logo">
                      <img src="/images/company-logo-2.svg" alt="" />
                    </div>
                  </div>
                  {/* Company Logo End */}
                  {/* Company Logo Start */}
                  <div className="swiper-slide">
                    <div className="partner-logo">
                      <img src="/images/company-logo-3.svg" alt="" />
                    </div>
                  </div>
                  {/* Company Logo End */}
                  {/* Company Logo Start */}
                  <div className="swiper-slide">
                    <div className="partner-logo">
                      <img src="/images/company-logo-4.svg" alt="" />
                    </div>
                  </div>
                  {/* Company Logo End */}
                  {/* Company Logo Start */}
                  <div className="swiper-slide">
                    <div className="partner-logo">
                      <img src="/images/company-logo-1.svg" alt="" />
                    </div>
                  </div>
                  {/* Company Logo End */}
                  {/* Company Logo Start */}
                  <div className="swiper-slide">
                    <div className="partner-logo">
                      <img src="/images/company-logo-2.svg" alt="" />
                    </div>
                  </div>
                  {/* Company Logo End */}
                  {/* Company Logo Start */}
                  <div className="swiper-slide">
                    <div className="partner-logo">
                      <img src="/images/company-logo-3.svg" alt="" />
                    </div>
                  </div>
                  {/* Company Logo End */}
                  {/* Company Logo Start */}
                  <div className="swiper-slide">
                    <div className="partner-logo">
                      <img src="/images/company-logo-4.svg" alt="" />
                    </div>
                  </div>
                  {/* Company Logo End */}
                </div>
              </div>
            </div>
            {/* Our Partners Slider End */}
          </div>
          {/* Our Partners Box End */}
        </div>
      </div>
    </div>
  </div>
  {/* Our Partners Section End */}
  {/* Our Testimonial Section Start */}
  <div className="our-testimonial">
    <div className="container">
      <div className="row section-row align-items-center">
        <div className="col-lg-6">
          {/* Section Title Start */}
          <div className="section-title">
            <h3 className="wow fadeInUp">our testimonials</h3>
            <h2
              className="wow fadeInUp"
              data-wow-delay="0.2s"
              data-cursor="-opaque"
            >
              Our clients speak real results <span>real protection</span>
            </h2>
          </div>
          {/* Section Title End */}
        </div>
        <div className="col-lg-6">
          {/* Customer Review Box Start */}
          <div className="customer-review-box testimonial-customer-box">
            {/* Customer Review Images Start */}
            <div className="customer-review-images">
              {/* Customer Image Start */}
              <div className="customer-image reveal">
                <figure className="image-anime">
                  <img src="/images/author-1.jpg" alt="" />
                </figure>
              </div>
              {/* Customer Image End */}
              {/* Customer Image Start */}
              <div className="customer-image reveal">
                <figure className="image-anime">
                  <img src="/images/author-2.jpg" alt="" />
                </figure>
              </div>
              {/* Customer Image End */}
              {/* Customer Image Start */}
              <div className="customer-image reveal">
                <figure className="image-anime">
                  <img src="/images/author-3.jpg" alt="" />
                </figure>
              </div>
              {/* Customer Image End */}
            </div>
            {/* Customer Review Images End */}
            {/* Customer Review Box Content Start */}
            <div className="customer-review-content">
              <p>More Than 1K+ trusted customers</p>
            </div>
            {/* Customer Review Box Content End */}
          </div>
          {/* Customer Review Box End */}
        </div>
      </div>
      <div className="row align-items-center">
        <div className="col-lg-4">
          {/* Testimonial Review Box Start */}
          <div className="testimonial-review-box">
            {/* Testimonial Review Header Start */}
            <div className="testimonial-review-header">
              <h2>
                <span className="counter">4.9</span>
              </h2>
              <div className="testimonial-rating">
                <i className="fa-solid fa-star" />
                <i className="fa-solid fa-star" />
                <i className="fa-solid fa-star" />
                <i className="fa-solid fa-star" />
                <i className="fa-solid fa-star" />
              </div>
              <p>
                (<span className="counter">40</span>+ Reviews)
              </p>
            </div>
            {/* Testimonial Review Header End */}
            {/* Testimonial Review Image Start */}
            <div className="customer-review-images">
              <div className="customer-image">
                <figure className="image-anime reveal">
                  <img src="/images/author-1.jpg" alt="" />
                </figure>
              </div>
              <div className="customer-image">
                <figure className="image-anime reveal">
                  <img src="/images/author-2.jpg" alt="" />
                </figure>
              </div>
              <div className="customer-image">
                <figure className="image-anime reveal">
                  <img src="/images/author-3.jpg" alt="" />
                </figure>
              </div>
              <div className="customer-image">
                <figure className="image-anime reveal">
                  <img src="/images/author-4.jpg" alt="" />
                </figure>
              </div>
              <div className="customer-image">
                <figure className="image-anime reveal">
                  <img src="/images/author-5.jpg" alt="" />
                </figure>
              </div>
            </div>
            {/* Testimonial Review Image End */}
            {/* Testimonial Review Content Start */}
            <div className="testimonial-review-content wow fadeInUp">
              <h3>Customer experiences that speak for themselves</h3>
            </div>
            {/* Testimonial Review Content End */}
          </div>
          {/* Testimonial Review Box End */}
        </div>
        <div className="col-lg-8">
          {/* Testimonial Slider Start */}
          <div className="testimonial-slider">
            <div className="swiper">
              <div className="swiper-wrapper" data-cursor-text="Drag">
                {/* Testimonial Slide Start */}
                <div className="swiper-slide">
                  <div className="testimonial-item">
                    <div className="testimonial-company-logo">
                      <img src="/images/icon-testimonial-logo.svg" alt="" />
                    </div>
                    <div className="testimonial-content">
                      <p>
                        The team transformed our brand's online presence with
                        creativity &amp; precision. The results exceeded our
                        expectations! Their digital marketing strategies helped
                        us reach a broader audience &amp; significantly boosted
                        our sales.
                      </p>
                    </div>
                    <div className="testimonial-body">
                      <div className="author-image">
                        <figure className="image-anime">
                          <img src="/images/author-1.jpg" alt="" />
                        </figure>
                      </div>
                      <div className="author-content">
                        <h3>Sarah Mitchell</h3>
                        <p>Marketing Director</p>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Testimonial Slide End */}
                {/* Testimonial Slide Start */}
                <div className="swiper-slide">
                  <div className="testimonial-item">
                    <div className="testimonial-company-logo">
                      <img src="/images/icon-testimonial-logo.svg" alt="" />
                    </div>
                    <div className="testimonial-content">
                      <p>
                        The team transformed our brand's online presence with
                        creativity &amp; precision. The results exceeded our
                        expectations! Their digital marketing strategies helped
                        us reach a broader audience &amp; significantly boosted
                        our sales.
                      </p>
                    </div>
                    <div className="testimonial-body">
                      <div className="author-image">
                        <figure className="image-anime">
                          <img src="/images/author-2.jpg" alt="" />
                        </figure>
                      </div>
                      <div className="author-content">
                        <h3>Ronald richards</h3>
                        <p>finance manager</p>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Testimonial Slide End */}
              </div>
              <div className="testimonial-btn">
                <div className="testimonial-button-prev" />
                <div className="testimonial-button-next" />
              </div>
            </div>
          </div>
          {/* Testimonial Slider End */}
        </div>
      </div>
    </div>
  </div>
  {/* Our Testimonial Section End */}
  {/* Our Faqs Section Start */}
  <div className="our-faqs">
    <div className="container">
      <div className="row">
        <div className="col-lg-6">
          {/* Faqs Content Start */}
          <div className="faqs-content">
            {/* Section Title Start */}
            <div className="section-title">
              <h3 className="wow fadeInUp">Frequently Asked Questions</h3>
              <h2
                className="wow fadeInUp"
                data-wow-delay="0.2s"
                data-cursor="-opaque"
              >
                Your complete FAQ guide to <span> cybersecurity solutions</span>
              </h2>
            </div>
            {/* Section Title End */}
            {/* Faqs Contact Box Start */}
            <div className="faqs-contact-box">
              {/* Faqs Contact Item Start */}
              <div
                className="faqs-contact-item wow fadeInUp"
                data-wow-delay="0.4s"
              >
                <div className="icon-box">
                  <img src="/images/sidebar-cta-img.svg" alt="" />
                </div>
                <div className="faqs-contact-item-content">
                  <h3>24/7 Customer Care</h3>
                  <p>
                    <a href="tel:659888695">(+1) 659-888-695</a>
                  </p>
                </div>
              </div>
              {/* Faqs Contact Item End */}
              {/* Faqs Contact Item Start */}
              <div
                className="faqs-contact-item wow fadeInUp"
                data-wow-delay="0.6s"
              >
                <div className="icon-box">
                  <img src="/images/icon-mail.svg" alt="" />
                </div>
                <div className="faqs-contact-item-content">
                  <h3>Technical Issues</h3>
                  <p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                  </p>
                </div>
              </div>
              {/* Faqs Contact Item End */}
            </div>
            {/* Faqs Contact Box End */}
            {/* Faqs Image Start */}
            <div className="faqs-image">
              <figure className="image-anime reveal">
                <img src="/images/faqs-image.jpg" alt="" />
              </figure>
            </div>
            {/* Faqs Image End */}
          </div>
          {/* Faqs Content End */}
        </div>
        <div className="col-lg-6">
          {/* FAQs section start */}
          <div className="faq-accordion page-faq-accordion" id="faq1">
            <div className="section-title">
              <h2 className="wow fadeInUp" data-cursor="-opaque">
                General <span>questions</span>
              </h2>
            </div>
            {/* FAQ Accordion Start */}
            <div className="faq-accordion" id="accordion">
              {/* FAQ Item Start */}
              <div
                className="accordion-item wow fadeInUp"
                data-wow-delay="0.2s"
              >
                <h2 className="accordion-header" id="heading1">
                  <button
                    className={`accordion-button ${openPotentialAccordion === 'collapse1' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handlePotentialAccordionToggle('collapse1')}
                    aria-expanded={openPotentialAccordion === 'collapse1'}
                    aria-controls="collapse1"
                  >
                    What is cybersecurity, and why is it important?
                  </button>
                </h2>
                <div
                  id="collapse1"
                  className={`accordion-collapse ${openPotentialAccordion === 'collapse1' ? 'show' : 'collapse'}`}
                  aria-labelledby="heading1"
                >
                  <div className="accordion-body">
                    <p>
                      Cybersecurity is the practice of protecting systems, networks, and programs from digital attacks. These cyberattacks are usually aimed at accessing, changing, or destroying sensitive information, extorting money from users, or interrupting normal business processes. It's important because in today's interconnected world, everyone benefits from advanced cyber defense programs.
                    </p>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
              {/* FAQ Item Start */}
              <div
                className="accordion-item wow fadeInUp"
                data-wow-delay="0.4s"
              >
                <h2 className="accordion-header" id="heading2">
                  <button
                    className={`accordion-button ${openPotentialAccordion === 'collapse2' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handlePotentialAccordionToggle('collapse2')}
                    aria-expanded={openPotentialAccordion === 'collapse2'}
                    aria-controls="collapse2"
                  >
                    Do small businesses need cybersecurity?
                  </button>
                </h2>
                <div
                  id="collapse2"
                  className={`accordion-collapse ${openPotentialAccordion === 'collapse2' ? 'show' : 'collapse'}`}
                  aria-labelledby="heading2"
                >
                  <div className="accordion-body">
                    <p>
                      Yes, small businesses need cybersecurity just as much as larger enterprises. Cybercriminals often target smaller businesses because they may have weaker security defenses. A data breach or cyber attack can lead to financial loss, reputational damage, and legal consequences.
                    </p>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
              {/* FAQ Item Start */}
              <div
                className="accordion-item wow fadeInUp"
                data-wow-delay="0.6s"
              >
                <h2 className="accordion-header" id="heading3">
                  <button
                    className={`accordion-button ${openPotentialAccordion === 'collapse3' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handlePotentialAccordionToggle('collapse3')}
                    aria-expanded={openPotentialAccordion === 'collapse3'}
                    aria-controls="collapse3"
                  >
                    What is ransomware, and how can I protect against it?
                  </button>
                </h2>
                <div
                  id="collapse3"
                  className={`accordion-collapse ${openPotentialAccordion === 'collapse3' ? 'show' : 'collapse'}`}
                  aria-labelledby="heading3"
                >
                  <div className="accordion-body">
                    <p>
                      Ransomware is malicious software that encrypts your files and demands payment to restore access. To protect against it, keep software updated, use strong passwords, enable two-factor authentication, regularly backup data, and be cautious of suspicious emails and links.
                    </p>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
              {/* FAQ Item Start */}
              <div
                className="accordion-item wow fadeInUp"
                data-wow-delay="0.8s"
              >
                <h2 className="accordion-header" id="heading4">
                  <button
                    className={`accordion-button ${openPotentialAccordion === 'collapse4' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handlePotentialAccordionToggle('collapse4')}
                    aria-expanded={openPotentialAccordion === 'collapse4'}
                    aria-controls="collapse4"
                  >
                    How do I know if my business has been hacked?
                  </button>
                </h2>
                <div
                  id="collapse4"
                  className={`accordion-collapse ${openPotentialAccordion === 'collapse4' ? 'show' : 'collapse'}`}
                  aria-labelledby="heading4"
                >
                  <div className="accordion-body">
                    <p>
                      Signs of a hack include unusual account activity, unexpected password changes, slow system performance, pop-up messages, disabled security software, and unauthorized transactions. If you suspect a breach, immediately disconnect from the internet and contact cybersecurity professionals.
                    </p>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
              {/* FAQ Item Start */}
              <div
                className="accordion-item wow fadeInUp"
                data-wow-delay="1s"
              >
                <h2 className="accordion-header" id="heading5">
                  <button
                    className={`accordion-button ${openPotentialAccordion === 'collapse5' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handlePotentialAccordionToggle('collapse5')}
                    aria-expanded={openPotentialAccordion === 'collapse5'}
                    aria-controls="collapse5"
                  >
                    How often should I update my cybersecurity?
                  </button>
                </h2>
                <div
                  id="collapse5"
                  className={`accordion-collapse ${openPotentialAccordion === 'collapse5' ? 'show' : 'collapse'}`}
                  aria-labelledby="heading5"
                >
                  <div className="accordion-body">
                    <p>
                      Cybersecurity should be updated continuously. Software updates should be applied as soon as they're available, security policies should be reviewed quarterly, and comprehensive security audits should be conducted annually. Stay informed about emerging threats and adjust your security measures accordingly.
                    </p>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
            </div>
            {/* FAQ Accordion End */}
          </div>
          {/* FAQs section End */}
        </div>
      </div>
    </div>
  </div>
      {/* Our Faqs Section End */}

      <Footer />
    </>
  );
}
