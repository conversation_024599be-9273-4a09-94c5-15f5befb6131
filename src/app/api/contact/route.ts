import { NextRequest, NextResponse } from 'next/server';
import { sendEmail } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { fname, lname, phone, email, message } = body;

    // Validation
    const errors: string[] = [];
    
    if (!fname || fname.trim() === '') {
      errors.push('First Name is required.');
    }
    
    if (!lname || lname.trim() === '') {
      errors.push('Last Name is required.');
    }
    
    if (!phone || phone.trim() === '') {
      errors.push('Phone is required.');
    }
    
    if (!email || email.trim() === '') {
      errors.push('Email is required.');
    } else if (!isValidEmail(email)) {
      errors.push('Please enter a valid email address.');
    }
    
    if (!message || message.trim() === '') {
      errors.push('Message is required.');
    }

    if (errors.length > 0) {
      return NextResponse.json(
        { success: false, message: errors.join(' ') },
        { status: 400 }
      );
    }

    // Prepare email content
    const subject = process.env.CONTACT_SUBJECT || 'Contact Inquiry from Technoloway Website';
    const toEmail = process.env.CONTACT_EMAIL || '<EMAIL>';
    
    const emailBody = `
Contact Form Submission from Technoloway Website

First Name: ${fname}
Last Name: ${lname}
Phone: ${phone}
Email: ${email}

Message:
${message}

---
This message was sent from the contact form on the Technoloway website.
    `.trim();

    // For now, we'll log the email content
    // In production, you would integrate with an email service
    console.log('Contact Form Submission:', {
      to: toEmail,
      subject,
      body: emailBody,
      from: email
    });

    // Send email using configured service
    try {
      await sendEmail({
        to: toEmail,
        subject,
        text: emailBody,
        from: email
      });
    } catch (emailError) {
      console.error('Email sending failed:', emailError);
      // For now, we'll still return success but log the error
      // In production, you might want to handle this differently
    }

    return NextResponse.json(
      { 
        success: true, 
        message: 'Thank you for your message. We will get back to you soon!' 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Contact form error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Something went wrong. Please try again later.' 
      },
      { status: 500 }
    );
  }
}

// Email validation helper
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
