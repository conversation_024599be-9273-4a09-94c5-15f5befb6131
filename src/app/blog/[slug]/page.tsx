'use client';

import { useEffect } from 'react';
import { use } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/layout/Footer';
import BlogPageHeader from '@/components/layout/BlogPageHeader';
import ScrollingTicker from '@/components/layout/ScrollingTicker';
import { useScriptInitialization } from '@/hooks/useScriptInitialization';
import { highlightBlogTitle } from '@/utils/titleHighlighting';

// Blog data - you can expand this with more blog posts
const blogData = {
  'top-cybersecurity-trends-2025': {
    title: 'Top Cybersecurity Trends to Watch in 2025',
    image: '/images/post-1.jpg',
    content: `
      <p className="wow fadeInUp">Cybersecurity is constantly evolving. Explore the key trends and technologies set to shape the security landscape in 2025 and beyond. Whether you're a business owner or an IT professional, understanding these trends is crucial for maintaining robust defenses against ever-growing cyber threats.</p>
      <p className="wow fadeInUp" data-wow-delay="0.2s">By keeping an eye on these trends and integrating them into your cybersecurity strategies, you can stay one step ahead of cybercriminals and better protect your business in an increasingly interconnected world.</p>
      <blockquote className="wow fadeInUp" data-wow-delay="0.4s"><p>Stay ahead of evolving threats. Contact our team of cybersecurity experts today to learn how we can help you implement the latest security technologies and protect your business in 2025 and beyond.</p></blockquote>
      <p className="wow fadeInUp" data-wow-delay="0.6s">The cybersecurity landscape is evolving quickly, and businesses must stay proactive in adopting new technologies and strategies to combat emerging threats.</p>
      <h2 className="wow fadeInUp" data-wow-delay="0.8s">Securing Your Future with Cyber</h2>
      <p className="wow fadeInUp" data-wow-delay="1s">In today's fast-paced digital world, cybersecurity is no longer optional—it's a necessity. "Securing Your Future with Cyber" is more than just a slogan; it's a commitment to building a resilient infrastructure.</p>
    `
  },
  'detect-prevent-phishing-scams': {
    title: 'How to Detect and Prevent Phishing Scams',
    image: '/images/post-2.jpg',
    content: `
      <p className="wow fadeInUp">Phishing attacks continue to be one of the most common and effective cyber threats. Learn essential strategies to identify and protect against sophisticated phishing attacks that target both individuals and organizations.</p>
      <p className="wow fadeInUp" data-wow-delay="0.2s">Understanding the psychology behind phishing attacks and implementing proper training can significantly reduce your risk of falling victim to these scams.</p>
      <blockquote className="wow fadeInUp" data-wow-delay="0.4s"><p>Education is your first line of defense against phishing attacks. Regular training and awareness programs can help your team recognize and avoid these threats.</p></blockquote>
      <h2 className="wow fadeInUp" data-wow-delay="0.8s">Common Phishing Techniques</h2>
      <p className="wow fadeInUp" data-wow-delay="1s">Cybercriminals use various techniques to trick their victims, including email spoofing, social engineering, and creating fake websites that look legitimate.</p>
    `
  },
  'regular-security-audits-critical': {
    title: 'Why Regular Security Audits Are Critical for Your Business',
    image: '/images/post-3.jpg',
    content: `
      <p className="wow fadeInUp">Regular security audits are essential for identifying vulnerabilities and ensuring your cybersecurity measures are up to date. Discover why ongoing security assessments are crucial for maintaining robust cyber defenses.</p>
      <p className="wow fadeInUp" data-wow-delay="0.2s">A comprehensive security audit can reveal weaknesses in your systems, processes, and policies that could be exploited by cybercriminals.</p>
      <blockquote className="wow fadeInUp" data-wow-delay="0.4s"><p>Prevention is always better than cure. Regular security audits help you identify and fix vulnerabilities before they can be exploited.</p></blockquote>
      <h2 className="wow fadeInUp" data-wow-delay="0.8s">Components of a Security Audit</h2>
      <p className="wow fadeInUp" data-wow-delay="1s">A thorough security audit should examine your network infrastructure, applications, data protection measures, and employee security awareness.</p>
    `
  },
  'build-strong-cybersecurity-culture': {
    title: 'How to Build a Strong Cybersecurity Culture in Your Company',
    image: '/images/post-4.jpg',
    content: `
      <p className="wow fadeInUp">Creating a security-first mindset across your organization is essential for comprehensive protection. Learn proven strategies to build a strong cybersecurity culture that engages all employees.</p>
      <p className="wow fadeInUp" data-wow-delay="0.2s">A strong cybersecurity culture starts from the top and permeates throughout the entire organization, making security everyone's responsibility.</p>
      <blockquote className="wow fadeInUp" data-wow-delay="0.4s"><p>Your employees are your greatest security asset. Invest in their education and create a culture where security is everyone's priority.</p></blockquote>
      <h2 className="wow fadeInUp" data-wow-delay="0.8s">Building Security Awareness</h2>
      <p className="wow fadeInUp" data-wow-delay="1s">Regular training, clear policies, and ongoing communication are key to building and maintaining a strong cybersecurity culture.</p>
    `
  },
  'securing-remote-workforce-2025': {
    title: 'Securing Your Remote Workforce: Best Practices for 2025',
    image: '/images/post-5.jpg',
    content: `
      <p className="wow fadeInUp">With remote work becoming the new normal, securing distributed teams requires new approaches and technologies. Implement effective security measures for your remote workforce in the modern workplace.</p>
      <p className="wow fadeInUp" data-wow-delay="0.2s">Remote work introduces unique security challenges that require specialized solutions and policies to protect your organization's data and systems.</p>
      <blockquote className="wow fadeInUp" data-wow-delay="0.4s"><p>Remote work security requires a combination of technology, policies, and employee education to be truly effective.</p></blockquote>
      <h2 className="wow fadeInUp" data-wow-delay="0.8s">Remote Work Security Challenges</h2>
      <p className="wow fadeInUp" data-wow-delay="1s">From unsecured home networks to personal devices, remote work presents numerous security challenges that must be addressed.</p>
    `
  },
  'impact-cyber-attacks-businesses': {
    title: 'Understanding the Impact of Cyber Attacks on Businesses',
    image: '/images/post-6.jpg',
    content: `
      <p className="wow fadeInUp">Cyber attacks can have devastating consequences for businesses of all sizes. Explore the real-world impact of cyber attacks and learn how to mitigate their effects on your organization.</p>
      <p className="wow fadeInUp" data-wow-delay="0.2s">From financial losses to reputational damage, the consequences of a cyber attack can be far-reaching and long-lasting.</p>
      <blockquote className="wow fadeInUp" data-wow-delay="0.4s"><p>Preparation is key. Having a comprehensive incident response plan can significantly reduce the impact of a cyber attack on your business.</p></blockquote>
      <h2 className="wow fadeInUp" data-wow-delay="0.8s">Types of Business Impact</h2>
      <p className="wow fadeInUp" data-wow-delay="1s">Cyber attacks can affect your business in multiple ways, including financial losses, operational disruption, and damage to customer trust.</p>
    `
  }
};

interface BlogPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default function BlogPage({ params }: BlogPageProps) {
  const isScriptsInitialized = useScriptInitialization();
  const { slug } = use(params);
  
  // Reset magic cursor state when page loads
  useEffect(() => {
    const resetCursorState = () => {
      if (typeof window !== 'undefined' && (window as any).cursor) {
        (window as any).cursor.removeState('-text');
        (window as any).cursor.removeText();
      }
    };
    
    // Reset immediately and after a short delay
    resetCursorState();
    setTimeout(resetCursorState, 100);
    setTimeout(resetCursorState, 500);
  }, []);
  
  // Get blog data based on slug
  const post = blogData[slug as keyof typeof blogData];
  
  // If post not found, you could redirect to 404 or show a default
  if (!post) {
    return (
      <div className="container mt-5">
        <h1>Blog post not found</h1>
        <Link href="/blog">Back to blog</Link>
      </div>
    );
  }

  return (
    <>
      
      {/* Preloader Start */}
      <div className="preloader">
        <div className="loading-container">
          <div className="loading" />
          <div id="loading-icon">
            <Image src="/images/loader.svg" alt="Loading" width={50} height={50} />
          </div>
        </div>
      </div>
      {/* Preloader End */}

      <BlogPageHeader 
        title={highlightBlogTitle(post.title).title}
        titleHighlight={highlightBlogTitle(post.title).titleHighlight}
        breadcrumbs={[
          { label: 'home', href: '/' },
          { label: 'blog', href: '/blog' },
          { label: post.title, isActive: true }
        ]}
      />
      <ScrollingTicker />

      {/* Page Single Post Start */}
      <div className="page-single-post blog-page">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              {/* Post Featured Image Start */}
              <div className="post-image">
                <figure className="image-anime reveal">
                  <Image src={post.image} alt={post.title} width={800} height={400} />
                </figure>
              </div>
              {/* Post Featured Image End */}
              {/* Post Single Content Start */}
              <div className="post-content">
                {/* Post Entry Start */}
                <div className="post-entry" dangerouslySetInnerHTML={{ __html: post.content }} />
                {/* Post Entry End */}
                {/* Post Tag Links Start */}
                <div className="post-tag-links">
                  <div className="row align-items-center">
                    <div className="col-lg-8">
                      {/* Post Tags Start */}
                      <div className="post-tags wow fadeInUp" data-wow-delay="0.5s">
                        <span className="tag-links">
                          Tags:
                          <a href="#">SafeBusiness</a>
                          <a href="#">ThreatFree</a>
                          <a href="#">SecureTech</a>
                        </span>
                      </div>
                      {/* Post Tags End */}
                    </div>
                    <div className="col-lg-4">
                      {/* Post Social Links Start */}
                      <div
                        className="post-social-sharing wow fadeInUp"
                        data-wow-delay="0.5s"
                      >
                        <ul>
                          <li>
                            <a href="#">
                              <i className="fa-brands fa-facebook-f" />
                            </a>
                          </li>
                          <li>
                            <a href="#">
                              <i className="fa-brands fa-linkedin-in" />
                            </a>
                          </li>
                          <li>
                            <a href="#">
                              <i className="fa-brands fa-instagram" />
                            </a>
                          </li>
                          <li>
                            <a href="#">
                              <i className="fa-brands fa-x-twitter" />
                            </a>
                          </li>
                        </ul>
                      </div>
                      {/* Post Social Links End */}
                    </div>
                  </div>
                </div>
                {/* Post Tag Links End */}
              </div>
              {/* Post Single Content End */}
            </div>
          </div>
        </div>
      </div>
      {/* Page Single Post End */}

      <Footer />
    </>
  );
}
