'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import ScrollingTicker from '@/components/layout/ScrollingTicker';
import { useScriptInitialization } from '@/hooks/useScriptInitialization';


export default function Blogs() {
  const isScriptsInitialized = useScriptInitialization();

  return (
    <>
    {/* Preloader Start */}
  <div className="preloader">
    <div className="loading-container">
      <div className="loading" />
      <div id="loading-icon">
        <img src="/images/loader.svg" alt="" />
      </div>
    </div>
  </div>
  {/* Preloader End */}

      <PageHeader 
        title="Our"
        titleHighlight="blog"
        breadcrumbs={[
          { label: 'home', href: '/' },
          { label: 'blog', isActive: true }
        ]}
      />
      <ScrollingTicker />

      <>
  
  
  {/* Page Blog Start */}
  <div className="page-blog">
    <div className="container">
      <div className="row">
        <div className="col-lg-4 col-md-6">
          {/* Post Item Start */}
          <div className="post-item wow fadeInUp">
            {/* Post Featured Image Start*/}
            <div className="post-featured-image">
              <Link href="/blog/top-cybersecurity-trends-2025" data-cursor-text="View">
                <figure className="image-anime">
                  <Image src="/images/post-1.jpg" alt="Top Cybersecurity Trends to Watch in 2025" width={400} height={250} />
                </figure>
              </Link>
            </div>
            {/* Post Featured Image End */}
            {/* Post Item Body Start */}
            <div className="post-item-body">
              {/* Post Item Content Start */}
              <div className="post-item-content">
                <h2>
                  <Link href="/blog/top-cybersecurity-trends-2025">
                    Top Cybersecurity Trends to Watch in 2025
                  </Link>
                </h2>
              </div>
              {/* Post Item Content End */}
              {/* Post Item Readmore Button Start*/}
              <div className="post-item-btn">
                <Link href="/blog/top-cybersecurity-trends-2025" className="readmore-btn">
                  read more
                </Link>
              </div>
              {/* Post Item Readmore Button End*/}
            </div>
            {/* Post Item Body End */}
          </div>
          {/* Post Item End */}
        </div>
        <div className="col-lg-4 col-md-6">
          {/* Post Item Start */}
          <div className="post-item wow fadeInUp" data-wow-delay="0.2s">
            {/* Post Featured Image Start*/}
            <div className="post-featured-image">
              <Link href="/blog/detect-prevent-phishing-scams" data-cursor-text="View">
                <figure className="image-anime">
                  <Image src="/images/post-2.jpg" alt="How to Detect and Prevent Phishing Scams" width={400} height={250} />
                </figure>
              </Link>
            </div>
            {/* Post Featured Image End */}
            {/* Post Item Body Start */}
            <div className="post-item-body">
              {/* Post Item Content Start */}
              <div className="post-item-content">
                <h2>
                  <Link href="/blog/detect-prevent-phishing-scams">
                    How to Detect and Prevent Phishing Scams
                  </Link>
                </h2>
              </div>
              {/* Post Item Content End */}
              {/* Post Item Readmore Button Start*/}
              <div className="post-item-btn">
                <Link href="/blog/detect-prevent-phishing-scams" className="readmore-btn">
                  read more
                </Link>
              </div>
              {/* Post Item Readmore Button End*/}
            </div>
            {/* Post Item Body End */}
          </div>
          {/* Post Item End */}
        </div>
        <div className="col-lg-4 col-md-6">
          {/* Post Item Start */}
          <div className="post-item wow fadeInUp" data-wow-delay="0.4s">
            {/* Post Featured Image Start*/}
            <div className="post-featured-image">
              <Link href="/blog/regular-security-audits-critical" data-cursor-text="View">
                <figure className="image-anime">
                  <Image src="/images/post-3.jpg" alt="Why Regular Security Audits Are Critical for Your Business" width={400} height={250} />
                </figure>
              </Link>
            </div>
            {/* Post Featured Image End */}
            {/* Post Item Body Start */}
            <div className="post-item-body">
              {/* Post Item Content Start */}
              <div className="post-item-content">
                <h2>
                  <Link href="/blog/regular-security-audits-critical">
                    Why Regular Security Audits Are Critical for Your Business
                  </Link>
                </h2>
              </div>
              {/* Post Item Content End */}
              {/* Post Item Readmore Button Start*/}
              <div className="post-item-btn">
                <Link href="/blog/regular-security-audits-critical" className="readmore-btn">
                  read more
                </Link>
              </div>
              {/* Post Item Readmore Button End*/}
            </div>
            {/* Post Item Body End */}
          </div>
          {/* Post Item End */}
        </div>
        <div className="col-lg-4 col-md-6">
          {/* Post Item Start */}
          <div className="post-item wow fadeInUp" data-wow-delay="0.6s">
            {/* Post Featured Image Start*/}
            <div className="post-featured-image">
              <Link href="/blog/build-strong-cybersecurity-culture" data-cursor-text="View">
                <figure className="image-anime">
                  <Image src="/images/post-4.jpg" alt="How to Build a Strong Cybersecurity Culture in Your Company" width={400} height={250} />
                </figure>
              </Link>
            </div>
            {/* Post Featured Image End */}
            {/* Post Item Body Start */}
            <div className="post-item-body">
              {/* Post Item Content Start */}
              <div className="post-item-content">
                <h2>
                  <Link href="/blog/build-strong-cybersecurity-culture">
                    How to Build a Strong Cybersecurity Culture in Your Company
                  </Link>
                </h2>
              </div>
              {/* Post Item Content End */}
              {/* Post Item Readmore Button Start*/}
              <div className="post-item-btn">
                <Link href="/blog/build-strong-cybersecurity-culture" className="readmore-btn">
                  read more
                </Link>
              </div>
              {/* Post Item Readmore Button End*/}
            </div>
            {/* Post Item Body End */}
          </div>
          {/* Post Item End */}
        </div>
        <div className="col-lg-4 col-md-6">
          {/* Post Item Start */}
          <div className="post-item wow fadeInUp" data-wow-delay="0.8s">
            {/* Post Featured Image Start*/}
            <div className="post-featured-image">
              <Link href="/blog/securing-remote-workforce-2025" data-cursor-text="View">
                <figure className="image-anime">
                  <Image src="/images/post-5.jpg" alt="Securing Your Remote Workforce: Best Practices for 2025" width={400} height={250} />
                </figure>
              </Link>
            </div>
            {/* Post Featured Image End */}
            {/* Post Item Body Start */}
            <div className="post-item-body">
              {/* Post Item Content Start */}
              <div className="post-item-content">
                <h2>
                  <Link href="/blog/securing-remote-workforce-2025">
                    Securing Your Remote Workforce: Best Practices for 2025
                  </Link>
                </h2>
              </div>
              {/* Post Item Content End */}
              {/* Post Item Readmore Button Start*/}
              <div className="post-item-btn">
                <Link href="/blog/securing-remote-workforce-2025" className="readmore-btn">
                  read more
                </Link>
              </div>
              {/* Post Item Readmore Button End*/}
            </div>
            {/* Post Item Body End */}
          </div>
          {/* Post Item End */}
        </div>
        <div className="col-lg-4 col-md-6">
          {/* Post Item Start */}
          <div className="post-item wow fadeInUp" data-wow-delay="1s">
            {/* Post Featured Image Start*/}
            <div className="post-featured-image">
              <Link href="/blog/impact-cyber-attacks-businesses" data-cursor-text="View">
                <figure className="image-anime">
                  <Image src="/images/post-6.jpg" alt="Understanding the Impact of Cyber Attacks on Businesses" width={400} height={250} />
                </figure>
              </Link>
            </div>
            {/* Post Featured Image End */}
            {/* Post Item Body Start */}
            <div className="post-item-body">
              {/* Post Item Content Start */}
              <div className="post-item-content">
                <h2>
                  <Link href="/blog/impact-cyber-attacks-businesses">
                    Understanding the Impact of Cyber Attacks on Businesses
                  </Link>
                </h2>
              </div>
              {/* Post Item Content End */}
              {/* Post Item Readmore Button Start*/}
              <div className="post-item-btn">
                <Link href="/blog/impact-cyber-attacks-businesses" className="readmore-btn">
                  read more
                </Link>
              </div>
              {/* Post Item Readmore Button End*/}
            </div>
            {/* Post Item Body End */}
          </div>
          {/* Post Item End */}
        </div>
        <div className="col-lg-12">
          {/* Page Pagination Start */}
          <div className="page-pagination wow fadeInUp" data-wow-delay="1.2s">
            <ul className="pagination">
              <li>
                <a href="#">
                  <i className="fa-solid fa-arrow-left-long" />
                </a>
              </li>
              <li className="active">
                <a href="#">1</a>
              </li>
              <li>
                <a href="#">2</a>
              </li>
              <li>
                <a href="#">3</a>
              </li>
              <li>
                <a href="#">
                  <i className="fa-solid fa-arrow-right-long" />
                </a>
              </li>
            </ul>
          </div>
          {/* Page Pagination End */}
        </div>
      </div>
    </div>
  </div>
  {/* Page Blog End */}
</>

      <Footer />

    
 
    </>
  );
}
