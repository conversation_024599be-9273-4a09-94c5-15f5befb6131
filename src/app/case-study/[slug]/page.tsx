'use client';

import { useEffect } from 'react';
import { use } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import ScrollingTicker from '@/components/layout/ScrollingTicker';
import { useScriptInitialization } from '@/hooks/useScriptInitialization';
import { smartHighlightTitle } from '@/utils/titleHighlighting';
import { getCaseStudyBySlug } from '@/data/caseStudies';

interface CaseStudyPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default function CaseStudyPage({ params }: CaseStudyPageProps) {
  const isScriptsInitialized = useScriptInitialization();
  const { slug } = use(params);
  
  // Reset magic cursor state when page loads
  useEffect(() => {
    const resetCursorState = () => {
      if (typeof window !== 'undefined' && (window as any).cursor) {
        (window as any).cursor.removeState('-text');
        (window as any).cursor.removeText();
      }
    };
    
    // Reset immediately and after a short delay
    resetCursorState();
    setTimeout(resetCursorState, 100);
    setTimeout(resetCursorState, 500);
  }, []);
  
  // Get case study data based on slug
  const caseStudy = getCaseStudyBySlug(slug);
  
  // If case study not found, show 404
  if (!caseStudy) {
    return (
      <div className="container mt-5">
        <h1>Case study not found</h1>
        <Link href="/case-study">Back to case studies</Link>
      </div>
    );
  }

  return (
    <>
    {/* Preloader Start */}
  <div className="preloader">
    <div className="loading-container">
      <div className="loading" />
      <div id="loading-icon">
        <img src="/images/loader.svg" alt="" />
      </div>
    </div>
  </div>
  {/* Preloader End */}

      <PageHeader 
        title={smartHighlightTitle(caseStudy.title).title}
        titleHighlight={smartHighlightTitle(caseStudy.title).titleHighlight}
        breadcrumbs={[
          { label: 'home', href: '/' },
          { label: 'case study', href: '/case-study' },
          { label: caseStudy.title, isActive: true }
        ]}
      />
      <ScrollingTicker />

      <>
  
  
      <>
      <>
  {/* Page Case Study Single Start */}
  <div className="page-case-study-single">
    <div className="container">
      <div className="row">
        <div className="col-lg-4">
          {/* Page Single Sidebar Start */}
          <div className="page-single-sidebar">
            {/* case Study Category List Start */}
            <div className="case-study-category-list wow fadeInUp">
              <div className="case-study-category-item">
                <h3>project name</h3>
                <p>{caseStudy.projectName}</p>
              </div>
              <div className="case-study-category-item">
                <h3>Category</h3>
                <p>{caseStudy.category}</p>
              </div>
              <div className="case-study-category-item">
                <h3>clients</h3>
                <p>{caseStudy.client}</p>
              </div>
              <div className="case-study-category-item">
                <h3>date</h3>
                <p>{caseStudy.date}</p>
              </div>
              <div className="case-study-category-item">
                <h3>duration</h3>
                <p>{caseStudy.duration}</p>
              </div>
            </div>
            {/* case Study Category List End */}
            {/* Sidebar CTA Box Start */}
            <div
              className="sidebar-cta-box wow fadeInUp"
              data-wow-delay="0.25s"
            >
              {/* Sidebar CTA Image Start */}
              <div className="sidebar-cta-img">
                <img src="/images/sidebar-cta-img.svg" alt="" />
              </div>
              {/* Sidebar CTA Image End */}
              {/* Sidebar CTA Content Start */}
              <div className="sidebar-cta-content">
                <h3>How Can We Help</h3>
                <p>
                  Whenever you need help, our team is just a message or call
                  away.
                </p>
              </div>
              {/* Sidebar CTA Content End */}
              {/* Sidebar CTA Contact Start */}
              <div className="sidebar-cta-contact">
                {/* Footwr Contact Item Start */}
                <div className="footer-contact-item">
                  <div className="icon-box">
                    <i className="fa-solid fa-phone" />
                  </div>
                  <div className="footer-contact-content">
                    <p>
                      <a href="tel:+001234567">+****************</a>
                    </p>
                  </div>
                </div>
                {/* Footwr Contact Item End */}
                {/* Footwr Contact Item Start */}
                <div className="footer-contact-item">
                  <div className="icon-box">
                    <i className="fa-solid fa-envelope" />
                  </div>
                  <div className="footer-contact-content">
                    <p>
                      <a href="mailto:<EMAIL>">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>
                {/* Footwr Contact Item End */}
              </div>
              {/* Sidebar CTA Contact End */}
            </div>
            {/* Sidebar CTA Box End */}
          </div>
          {/* Page Single Sidebar End */}
        </div>
        <div className="col-lg-8">
          {/* Case Study Content Start */}
          <div className="case-study-single-content">
            {/* Case Study Slider Start */}
            <div className="case-study-slider">
              <div className="swiper">
                <div className="swiper-wrapper">
                  {caseStudy.gallery.map((image, index) => (
                    <div key={index} className="swiper-slide">
                      <figure className="image-anime">
                        <img src={image} alt={`${caseStudy.title} - Image ${index + 1}`} />
                      </figure>
                    </div>
                  ))}
                </div>
                <div className="case-study-pagination" />
              </div>
            </div>
            {/* Case Study Slider End */}
            {/* Case Study Entry Start */}
            <div className="case-study-entry">
              <h2 className="wow fadeInUp">
                Information &amp; <span>overview</span>
              </h2>
              <p className="wow fadeInUp" data-wow-delay="0.2s">
                {caseStudy.overview}
              </p>
              {/* Case Study Solution Box Start */}
              <div className="case-study-solution-box">
                <h2 className="wow fadeInUp" data-wow-delay="0.4s">
                  Challenges &amp; <span>solution</span>
                </h2>
                <p className="wow fadeInUp" data-wow-delay="0.6s">
                  {caseStudy.challenges}
                </p>
                {/* Case Study Solution List Start */}
                <div className="case-study-solution-list">
                  {/* Case Study Solution Item Start */}
                  <div className="case-study-solution-item">
                    <h3 className="wow fadeInUp">Project Challenges :</h3>
                    <p className="wow fadeInUp" data-wow-delay="0.2s">
                      {caseStudy.challenges}
                    </p>
                    <ul className="wow fadeInUp" data-wow-delay="0.4s">
                      {caseStudy.solutions.map((solution, index) => (
                        <li key={index}>
                          {solution}
                        </li>
                      ))}
                    </ul>
                  </div>
                  {/* Case Study Solution Item End */}
                  {/* Case Study Solution Item Start */}
                  <div className="case-study-solution-item">
                    <h3 className="wow fadeInUp">Our solutions :</h3>
                    <p className="wow fadeInUp" data-wow-delay="0.2s">
                      Our comprehensive solutions address the specific challenges faced by {caseStudy.client} and deliver measurable results.
                    </p>
                    {/* Solution Item List Start */}
                    <div className="solution-item-list">
                      {caseStudy.solutions.map((solution, index) => (
                        <div
                          key={index}
                          className={`solution-item wow fadeInUp ${index === caseStudy.solutions.length - 1 ? 'highlighted-box' : ''}`}
                          data-wow-delay={`${(index + 2) * 0.2}s`}
                        >
                          <p>
                            {solution}
                          </p>
                        </div>
                      ))}
                    </div>
                    {/* Solution Item List End */}
                  </div>
                  {/* Case Study Solution Item End */}
                </div>
                {/* Case Study Solution List End */}
              </div>
              {/* Case Study Solution Box End */}
            </div>
            {/* Case Study Entry End */}
            {/* Page Single FAQs Start */}
            <div className="page-single-faqs">
              {/* Section Title Start */}
              <div className="section-title">
                <h2 className="wow fadeInUp" data-cursor="-opaque">
                  Your complete FAQ guide to{" "}
                  <span>cybersecurity solutions</span>
                </h2>
                <p className="wow fadeInUp" data-wow-delay="0.2s">
                  Welcome to our comprehensive FAQ guide on cybersecurity
                  solutions. Here we answer the most common questions about
                  protecting your business from cyber threats understanding the
                  latest security.
                </p>
              </div>
              {/* Section Title End */}
              {/* FAQ Accordion Start */}
              <div
                className="faq-accordion page-faq-accordion"
                id="faqaccordion"
              >
                {caseStudy.faqs.map((faq, index) => (
                  <div
                    key={index}
                    className="accordion-item wow fadeInUp"
                    data-wow-delay={`${index * 0.2}s`}
                  >
                    <h2 className="accordion-header" id={`heading${index + 1}`}>
                      <button
                        className={`accordion-button ${index === 0 ? '' : 'collapsed'}`}
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target={`#collapse${index + 1}`}
                        aria-expanded={index === 0 ? 'true' : 'false'}
                        aria-controls={`collapse${index + 1}`}
                      >
                        {faq.question}
                      </button>
                    </h2>
                    <div
                      id={`collapse${index + 1}`}
                      className={`accordion-collapse collapse ${index === 0 ? 'show' : ''}`}
                      aria-labelledby={`heading${index + 1}`}
                      data-bs-parent="#faqaccordion"
                    >
                      <div className="accordion-body">
                        <p>
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              {/* FAQ Accordion End */}
            </div>
            {/* Page Single FAQs End */}
          </div>
          {/* Case Study Content End */}
        </div>
      </div>
    </div>
  </div>
  {/* Page Case Study Single End */}
</>
</>
</>
      <Footer />

    
 
    </>
  );
}
