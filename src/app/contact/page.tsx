'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import ScrollingTicker from '@/components/layout/ScrollingTicker';
import { useScriptInitialization } from '@/hooks/useScriptInitialization';

export default function Contact() {
  const isScriptsInitialized = useScriptInitialization();
  const [formData, setFormData] = useState({
    fname: '',
    lname: '',
    phone: '',
    email: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
  }>({ type: null, message: '' });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus({ type: null, message: '' });

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        setSubmitStatus({
          type: 'success',
          message: result.message
        });
        setFormData({
          fname: '',
          lname: '',
          phone: '',
          email: '',
          message: ''
        });
      } else {
        setSubmitStatus({
          type: 'error',
          message: result.message
        });
      }
    } catch (error) {
      setSubmitStatus({
        type: 'error',
        message: 'Something went wrong. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {/* Preloader Start */}
      <div className="preloader">
        <div className="loading-container">
          <div className="loading" />
          <div id="loading-icon">
            <Image src="/images/loader.svg" alt="Loading" width={50} height={50} />
          </div>
        </div>
      </div>
      {/* Preloader End */}



      <PageHeader 
        title="Contact"
        titleHighlight="us"
        breadcrumbs={[
          { label: 'home', href: '/' },
          { label: 'contact us', isActive: true }
        ]}
      />

      <ScrollingTicker />

      <>
  {/* Page Contact Us Start */}
  <div className="page-contact-us">
    <div className="container">
      <div className="row align-items-center">
        <div className="col-lg-6">
          {/* Contact Us Form Start */}
          <div className="contact-us-form">
            {/* Section Title Start */}
            <div className="section-title">
              <h2 className="wow fadeInUp" data-cursor="-opaque">
                Get in <span>Touch Now!</span>
              </h2>
            </div>
            {/* Section Title End */}
            {/* Contact Form Start */}
            <div className="contact-form">
              <form
                id="contactForm"
                onSubmit={handleSubmit}
                className="wow fadeInUp"
                data-wow-delay="0.2s"
              >
                <div className="row">
                  <div className="form-group col-md-6 mb-4">
                    <input
                      type="text"
                      name="fname"
                      className="form-control"
                      id="fname"
                      placeholder="Enter Your First Name"
                      value={formData.fname}
                      onChange={handleInputChange}
                      required
                    />
                    <div className="help-block with-errors"></div>
                  </div>
                  <div className="form-group col-md-6 mb-4">
                    <input
                      type="text"
                      name="lname"
                      className="form-control"
                      id="lname"
                      placeholder="Enter Your Last Name"
                      value={formData.lname}
                      onChange={handleInputChange}
                      required
                    />
                    <div className="help-block with-errors" />
                  </div>
                  <div className="form-group col-md-6 mb-4">
                    <input
                      type="text"
                      name="phone"
                      className="form-control"
                      id="phone"
                      placeholder="Enter Your Phone Number"
                      value={formData.phone}
                      onChange={handleInputChange}
                      required
                    />
                    <div className="help-block with-errors" />
                  </div>
                  <div className="form-group col-md-6 mb-4">
                    <input
                      type="email"
                      name="email"
                      className="form-control"
                      id="email"
                      placeholder="Enter Your Email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                    <div className="help-block with-errors" />
                  </div>
                  <div className="form-group col-md-12 mb-5">
                    <textarea
                      name="message"
                      className="form-control"
                      id="message"
                      rows={6}
                      placeholder="Enter Your Message"
                      value={formData.message}
                      onChange={handleInputChange}
                    />
                    <div className="help-block with-errors" />
                  </div>
                  <div className="col-md-12">
                    <button 
                      type="submit" 
                      className="btn-default"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? 'Sending...' : 'Send Message'}
                    </button>
                    {submitStatus.type && (
                      <div 
                        className={`alert ${submitStatus.type === 'success' ? 'alert-success' : 'alert-danger'} mt-3`}
                        role="alert"
                      >
                        {submitStatus.message}
                      </div>
                    )}
                  </div>
                </div>
              </form>
            </div>
            {/* Contact Form End */}
          </div>
          {/* Contact Us Form End */}
        </div>
        <div className="col-lg-6">
          {/* Contact Us Content Start */}
          <div className="contact-us-content">
            {/* Section Title Start */}
            <div className="section-title">
              <h3 className="wow fadeInUp">Contact With Us</h3>
              <h2
                className="wow fadeInUp"
                data-wow-delay="0.2s"
                data-cursor="-opaque"
              >
                Get in touch with your digital <span>growth partners</span>
              </h2>
            </div>
            {/* Section Title End */}
            {/* Contact Info List Start */}
            <div className="contact-info-list">
              {/* Contact Info Item Start */}
              <div
                className="contact-info-item wow fadeInUp"
                data-wow-delay="0.4s"
              >
                {/* Icon Box Start */}
                <div className="icon-box">
                  <img src="/images/icon-phone.svg" alt="" />
                </div>
                {/* Icon Box End */}
                {/* Contact Item Content Start */}
                <div className="contact-item-content">
                  <p>contact us</p>
                  <h3>
                    <a href="tel:8333061816">+(1) ************</a>
                  </h3>
                </div>
                {/* Contact Item Content End */}
              </div>
              {/* Contact Info Item End */}
              {/* Contact Info Item Start */}
              <div
                className="contact-info-item wow fadeInUp"
                data-wow-delay="0.6s"
              >
                {/* Icon Box Start */}
                <div className="icon-box">
                  <img src="/images/icon-mail.svg" alt="" />
                </div>
                {/* Icon Box End */}
                {/* Contact Item Content Start */}
                <div className="contact-item-content">
                  <p>email us at</p>
                  <h3>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                  </h3>
                </div>
                {/* Contact Item Content End */}
              </div>
              {/* Contact Info Item End */}
              {/* Contact Info Item Start */}
              <div
                className="contact-info-item wow fadeInUp"
                data-wow-delay="0.8s"
              >
                {/* Icon Box Start */}
                <div className="icon-box">
                  <img src="/images/icon-location.svg" alt="" />
                </div>
                {/* Icon Box End */}
                {/* Contact Item Content Start */}
                <div className="contact-item-content">
                  <p>location</p>
                  <h3>Wichita, KS</h3>
                </div>
                {/* Contact Item Content End */}
              </div>
              {/* Contact Info Item End */}
            </div>
            {/* Contact Info List End */}
          </div>
          {/* Contact Us Content End */}
        </div>
        <div className="col-lg-12">
          {/* Google Map IFrame Start */}
          <div className="google-map-iframe">
            <iframe
  src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d44588.040015956554!2d-97.33052481205794!3d37.694635861004016!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sen!2sus!4v1755101682510!5m2!1sen!2sus"
  allowFullScreen
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
            />
          </div>
          {/* Google Map IFrame End */}
        </div>
      </div>
    </div>
  </div>
  {/* Page Contact Us End */}
</>


      <Footer />
    </>
  );
}
