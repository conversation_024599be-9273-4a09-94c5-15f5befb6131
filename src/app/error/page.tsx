'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import ScrollingTicker from '@/components/layout/ScrollingTicker';
import { useScriptInitialization } from '@/hooks/useScriptInitialization';

export default function About() {
  const isScriptsInitialized = useScriptInitialization();

  return (
    <>
      {/* Preloader Start */}
      <div className="preloader">
        <div className="loading-container">
          <div className="loading" />
          <div id="loading-icon">
            <Image src="/images/loader.svg" alt="Loading" width={50} height={50} />
          </div>
        </div>
      </div>
      {/* Preloader End */}



      <PageHeader 
        title="Page Not"
        titleHighlight="Found"
        breadcrumbs={[
          { label: 'home', href: '/' },
          { label: '404 error page', isActive: true }
        ]}
      />

      <ScrollingTicker />

      <>
  {/* error section start */}
  <div className="error-page">
    <div className="container">
      <div className="row justify-content-center">
        <div className="col-lg-10 text-center">
          <div className="error-page-image wow fadeInUp d-flex justify-content-center">
            <img src="/images/404-error-img.png" alt="" className="img-fluid" style={{ maxWidth: '400px', width: '100%' }} />
          </div>
          <div className="error-page-content">
            <div className="section-title">
              <h2
                className="wow fadeInUp"
                data-wow-delay="0.2s"
                data-cursor="-opaque"
              >
                Oops! Page <span>Not Found</span>
              </h2>
            </div>
            <div className="error-page-content-body">
              <p className="wow fadeInUp" data-wow-delay="0.4s">
                The page you are looking for does not exist.
              </p>
              <a
                className="btn-default wow fadeInUp"
                data-wow-delay="0.6s"
                href="./"
              >
                back to home
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* error section end */}
</>


      <Footer />
    </>
  );
}
