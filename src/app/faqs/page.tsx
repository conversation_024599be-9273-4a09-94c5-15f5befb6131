'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import ScrollingTicker from '@/components/layout/ScrollingTicker';
import { useScriptInitialization } from '@/hooks/useScriptInitialization';

export default function FAQs() {
  const isScriptsInitialized = useScriptInitialization();
  const [openAccordion, setOpenAccordion] = useState<string | null>('collapse1');

  const handleAccordionToggle = (accordionId: string) => {
    setOpenAccordion(openAccordion === accordionId ? null : accordionId);
  };

  return (
    <>
      {/* Preloader Start */}
      <div className="preloader">
        <div className="loading-container">
          <div className="loading" />
          <div id="loading-icon">
            <Image src="/images/loader.svg" alt="Loading" width={50} height={50} />
          </div>
        </div>
      </div>
      {/* Preloader End */}



      <PageHeader 
        title="Frequently asked"
        titleHighlight="questions"
        breadcrumbs={[
          { label: 'home', href: '/' },
          { label: 'faqs', isActive: true }
        ]}
      />

      <ScrollingTicker />

      <>
  {/* Page Faqs Start */}
  <div className="page-faqs">
    <div className="container">
      <div className="row">
        <div className="col-lg-4">
          {/* Page Single Sidebar Start */}
          <div className="page-single-sidebar">
            {/* Page Category List Start */}
            <div className="page-catagory-list wow fadeInUp">
              <ul>
                <li>
                  <a href="#faq1">General questions</a>
                </li>
                <li>
                  <a href="#faq2">Threat detection</a>
                </li>
                <li>
                  <a href="#faq3">Security services</a>
                </li>
                <li>
                  <a href="#faq4">Data protection</a>
                </li>
              </ul>
            </div>
            {/* Page Category List End */}
            {/* Sidebar CTA Box Start */}
            <div
              className="sidebar-cta-box wow fadeInUp"
              data-wow-delay="0.25s"
            >
              {/* Sidebar CTA Image Start */}
              <div className="sidebar-cta-img">
                <img src="/images/sidebar-cta-img.svg" alt="" />
              </div>
              {/* Sidebar CTA Image End */}
              {/* Sidebar CTA Content Start */}
              <div className="sidebar-cta-content">
                <h3>How Can We Help</h3>
                <p>
                  Whenever you need help, our team is just a message or call
                  away.
                </p>
              </div>
              {/* Sidebar CTA Content End */}
              {/* Sidebar CTA Contact Start */}
              <div className="sidebar-cta-contact">
                {/* Footwr Contact Item Start */}
                <div className="footer-contact-item">
                  <div className="icon-box">
                    <i className="fa-solid fa-phone" />
                  </div>
                  <div className="footer-contact-content">
                    <p>
                      <a href="tel:+001234567">+****************</a>
                    </p>
                  </div>
                </div>
                {/* Footwr Contact Item End */}
                {/* Footwr Contact Item Start */}
                <div className="footer-contact-item">
                  <div className="icon-box">
                    <i className="fa-solid fa-envelope" />
                  </div>
                  <div className="footer-contact-content">
                    <p>
                      <a href="mailto:<EMAIL>">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>
                {/* Footwr Contact Item End */}
              </div>
              {/* Sidebar CTA Contact End */}
            </div>
            {/* Sidebar CTA Box End */}
          </div>
          {/* Page Single Sidebar End */}
        </div>
        <div className="col-lg-8">
          {/* Page FAQs Catagery Start */}
          <div className="page-faqs-catagery">
            {/* FAQs section start */}
            <div className="faq-accordion page-faq-accordion" id="faq1">
              <div className="section-title">
                <h2 className="wow fadeInUp" data-cursor="-opaque">
                  General <span>questions</span>
                </h2>
              </div>
              {/* FAQ Accordion Start */}
              <div className="faq-accordion" id="accordion">
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.2s"
                >
                  <h2 className="accordion-header" id="heading1">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse1' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse1')}
                      aria-expanded={openAccordion === 'collapse1'}
                      aria-controls="collapse1"
                    >
                      What is cybersecurity, and why is it important?
                    </button>
                  </h2>
                  <div
                    id="collapse1"
                    className={`accordion-collapse ${openAccordion === 'collapse1' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading1"
                  >
                    <div className="accordion-body">
                      <p>
                        Cybersecurity is the practice of protecting systems, networks, and programs from digital attacks. These cyberattacks are usually aimed at accessing, changing, or destroying sensitive information, extorting money from users, or interrupting normal business processes. It's important because in today's interconnected world, everyone benefits from advanced cyber defense programs.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.4s"
                >
                  <h2 className="accordion-header" id="heading2">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse2' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse2')}
                      aria-expanded={openAccordion === 'collapse2'}
                      aria-controls="collapse2"
                    >
                      Do small businesses need cybersecurity?
                    </button>
                  </h2>
                  <div
                    id="collapse2"
                    className={`accordion-collapse ${openAccordion === 'collapse2' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading2"
                  >
                    <div className="accordion-body">
                      <p>
                        Yes, small businesses need cybersecurity just as much as larger enterprises. Cybercriminals often target smaller businesses because they may have weaker security defenses. A data breach or cyber attack can lead to financial loss, reputational damage, and legal consequences.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.6s"
                >
                  <h2 className="accordion-header" id="heading3">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse3' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse3')}
                      aria-expanded={openAccordion === 'collapse3'}
                      aria-controls="collapse3"
                    >
                      What is ransomware, and how can I protect against it?
                    </button>
                  </h2>
                  <div
                    id="collapse3"
                    className={`accordion-collapse ${openAccordion === 'collapse3' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading3"
                  >
                    <div className="accordion-body">
                      <p>
                        Ransomware is malicious software that encrypts your files and demands payment to restore access. To protect against it, keep software updated, use strong passwords, enable two-factor authentication, regularly backup data, and be cautious of suspicious emails and links.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.8s"
                >
                  <h2 className="accordion-header" id="heading4">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse4' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse4')}
                      aria-expanded={openAccordion === 'collapse4'}
                      aria-controls="collapse4"
                    >
                      How do I know if my business has been hacked?
                    </button>
                  </h2>
                  <div
                    id="collapse4"
                    className={`accordion-collapse ${openAccordion === 'collapse4' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading4"
                  >
                    <div className="accordion-body">
                      <p>
                        Signs of a hack include unusual account activity, unexpected password changes, slow system performance, pop-up messages, disabled security software, and unauthorized transactions. If you suspect a breach, immediately disconnect from the internet and contact cybersecurity professionals.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="1s"
                >
                  <h2 className="accordion-header" id="heading5">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse5' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse5')}
                      aria-expanded={openAccordion === 'collapse5'}
                      aria-controls="collapse5"
                    >
                      How often should I update my cybersecurity?
                    </button>
                  </h2>
                  <div
                    id="collapse5"
                    className={`accordion-collapse ${openAccordion === 'collapse5' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading5"
                  >
                    <div className="accordion-body">
                      <p>
                        Cybersecurity should be updated continuously. Software updates should be applied as soon as they're available, security policies should be reviewed quarterly, and comprehensive security audits should be conducted annually. Stay informed about emerging threats and adjust your security measures accordingly.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
              </div>
              {/* FAQ Accordion End */}
            </div>
            {/* FAQs section End */}
            {/* FAQs section start */}
            <div className="faq-accordion page-faq-accordion" id="faq2">
              <div className="section-title">
                <h2 className="wow fadeInUp" data-cursor="-opaque">
                  Threat <span>detection</span>
                </h2>
              </div>
              {/* FAQ Accordion Start */}
              <div className="faq-accordion" id="accordion1">
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.2s"
                >
                  <h2 className="accordion-header" id="heading6">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse6' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse6')}
                      aria-expanded={openAccordion === 'collapse6'}
                      aria-controls="collapse6"
                    >
                      What is threat detection?
                    </button>
                  </h2>
                  <div
                    id="collapse6"
                    className={`accordion-collapse ${openAccordion === 'collapse6' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading6"
                  >
                    <div className="accordion-body">
                      <p>
                        Threat detection is the process of identifying and analyzing potential security threats to your systems and networks. It involves monitoring for suspicious activities, analyzing patterns, and using various tools and techniques to identify potential cyber attacks before they can cause damage.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.4s"
                >
                  <h2 className="accordion-header" id="heading7">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse7' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse7')}
                      aria-expanded={openAccordion === 'collapse7'}
                      aria-controls="collapse7"
                    >
                      How does threat detection work?
                    </button>
                  </h2>
                  <div
                    id="collapse7"
                    className={`accordion-collapse ${openAccordion === 'collapse7' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading7"
                  >
                    <div className="accordion-body">
                      <p>
                        Threat detection works through continuous monitoring of network traffic, system logs, and user behavior. It uses machine learning algorithms, signature-based detection, and behavioral analysis to identify anomalies and potential threats. When a threat is detected, alerts are generated and response procedures are initiated.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.6s"
                >
                  <h2 className="accordion-header" id="heading8">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse8' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse8')}
                      aria-expanded={openAccordion === 'collapse8'}
                      aria-controls="collapse8"
                    >
                      Why is threat detection important?
                    </button>
                  </h2>
                  <div
                    id="collapse8"
                    className={`accordion-collapse ${openAccordion === 'collapse8' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading8"
                  >
                    <div className="accordion-body">
                      <p>
                        Threat detection is crucial because it allows organizations to identify and respond to cyber threats before they can cause significant damage. Early detection can prevent data breaches, financial losses, and reputational damage. It's a proactive approach to cybersecurity that helps maintain business continuity.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.8s"
                >
                  <h2 className="accordion-header" id="heading9">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse9' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse9')}
                      aria-expanded={openAccordion === 'collapse9'}
                      aria-controls="collapse9"
                    >
                      What are common types of threats?
                    </button>
                  </h2>
                  <div
                    id="collapse9"
                    className={`accordion-collapse ${openAccordion === 'collapse9' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading9"
                  >
                    <div className="accordion-body">
                      <p>
                        Common cyber threats include malware, phishing attacks, ransomware, DDoS attacks, insider threats, and advanced persistent threats (APTs). Each type requires different detection and response strategies, which is why comprehensive threat detection systems are essential.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="1s"
                >
                  <h2 className="accordion-header" id="heading10">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse10' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse10')}
                      aria-expanded={openAccordion === 'collapse10'}
                      aria-controls="collapse10"
                    >
                      What tools are used for threat detection?
                    </button>
                  </h2>
                  <div
                    id="collapse10"
                    className={`accordion-collapse ${openAccordion === 'collapse10' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading10"
                  >
                    <div className="accordion-body">
                      <p>
                        Common threat detection tools include Intrusion Detection Systems (IDS), Security Information and Event Management (SIEM) platforms, Endpoint Detection and Response (EDR) solutions, Network Traffic Analysis (NTA) tools, and threat intelligence platforms. These tools work together to provide comprehensive threat detection coverage.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
              </div>
              {/* FAQ Accordion End */}
            </div>
            {/* FAQs section End */}
            {/* FAQs section start */}
            <div className="faq-accordion page-faq-accordion" id="faq3">
              <div className="section-title">
                <h2 className="wow fadeInUp" data-cursor="-opaque">
                  Security <span>services</span>
                </h2>
              </div>
              {/* FAQ Accordion Start */}
              <div className="faq-accordion" id="accordion2">
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.2s"
                >
                  <h2 className="accordion-header" id="heading11">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse11' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse11')}
                      aria-expanded={openAccordion === 'collapse11'}
                      aria-controls="collapse11"
                    >
                      What types of security services do you offer?
                    </button>
                  </h2>
                  <div
                    id="collapse11"
                    className={`accordion-collapse ${openAccordion === 'collapse11' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading11"
                  >
                    <div className="accordion-body">
                      <p>
                        We offer comprehensive cybersecurity services including threat detection and response, security audits and assessments, penetration testing, incident response, security consulting, compliance management, and employee security training. Our services are tailored to meet the specific needs of businesses of all sizes.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.4s"
                >
                  <h2 className="accordion-header" id="heading12">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse12' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse12')}
                      aria-expanded={openAccordion === 'collapse12'}
                      aria-controls="collapse12"
                    >
                      Do you provide 24/7 security coverage?
                    </button>
                  </h2>
                  <div
                    id="collapse12"
                    className={`accordion-collapse ${openAccordion === 'collapse12' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading12"
                  >
                    <div className="accordion-body">
                      <p>
                        Yes, we provide 24/7 security monitoring and incident response services. Our security operations center (SOC) operates around the clock to detect, analyze, and respond to security threats in real-time. This ensures that your business is protected at all times, even outside of regular business hours.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.6s"
                >
                  <h2 className="accordion-header" id="heading13">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse13' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse13')}
                      aria-expanded={openAccordion === 'collapse13'}
                      aria-controls="collapse13"
                    >
                      What areas do you serve?
                    </button>
                  </h2>
                  <div
                    id="collapse13"
                    className={`accordion-collapse ${openAccordion === 'collapse13' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading13"
                  >
                    <div className="accordion-body">
                      <p>
                        We serve clients nationwide and internationally. Our cybersecurity services are delivered remotely, making them accessible to businesses anywhere in the world. We have local offices in major metropolitan areas and can provide on-site services when required for specific projects or emergency response situations.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.8s"
                >
                  <h2 className="accordion-header" id="heading14">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse14' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse14')}
                      aria-expanded={openAccordion === 'collapse14'}
                      aria-controls="collapse14"
                    >
                      How quickly can you respond to security incidents?
                    </button>
                  </h2>
                  <div
                    id="collapse14"
                    className={`accordion-collapse ${openAccordion === 'collapse14' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading14"
                  >
                    <div className="accordion-body">
                      <p>
                        Our incident response team can be activated within minutes of detecting a security threat. We have established response procedures that allow us to quickly assess the situation, contain the threat, and begin remediation. Response times vary based on the severity and type of incident, but we prioritize rapid response for all security events.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="1s"
                >
                  <h2 className="accordion-header" id="heading15">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse15' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse15')}
                      aria-expanded={openAccordion === 'collapse15'}
                      aria-controls="collapse15"
                    >
                      Do you offer customized security solutions?
                    </button>
                  </h2>
                  <div
                    id="collapse15"
                    className={`accordion-collapse ${openAccordion === 'collapse15' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading15"
                  >
                    <div className="accordion-body">
                      <p>
                        Yes, we specialize in creating customized security solutions tailored to your specific business needs, industry requirements, and risk profile. We conduct thorough assessments to understand your unique challenges and design security strategies that align with your business objectives and budget constraints.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
              </div>
              {/* FAQ Accordion End */}
            </div>
            {/* FAQs section End */}
            {/* FAQs section start */}
            <div className="faq-accordion page-faq-accordion" id="faq4">
              <div className="section-title">
                <h2 className="wow fadeInUp" data-cursor="-opaque">
                  Data <span>protection</span>
                </h2>
              </div>
              {/* FAQ Accordion Start */}
              <div className="faq-accordion" id="accordion3">
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.2s"
                >
                  <h2 className="accordion-header" id="heading16">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse16' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse16')}
                      aria-expanded={openAccordion === 'collapse16'}
                      aria-controls="collapse16"
                    >
                      What is data protection?
                    </button>
                  </h2>
                  <div
                    id="collapse16"
                    className={`accordion-collapse ${openAccordion === 'collapse16' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading16"
                  >
                    <div className="accordion-body">
                      <p>
                        Data protection refers to the practices, safeguards, and binding rules put in place to protect your personal information and ensure that you remain in control of it. It involves implementing security measures to prevent unauthorized access, use, disclosure, alteration, or destruction of personal data.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.4s"
                >
                  <h2 className="accordion-header" id="heading17">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse17' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse17')}
                      aria-expanded={openAccordion === 'collapse17'}
                      aria-controls="collapse17"
                    >
                      Why is data protection important?
                    </button>
                  </h2>
                  <div
                    id="collapse17"
                    className={`accordion-collapse ${openAccordion === 'collapse17' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading17"
                  >
                    <div className="accordion-body">
                      <p>
                        Data protection is crucial because it safeguards individuals' privacy rights, prevents identity theft, protects against financial fraud, ensures compliance with regulations like GDPR and HIPAA, and maintains customer trust. For businesses, it's essential for maintaining reputation and avoiding costly legal consequences.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.6s"
                >
                  <h2 className="accordion-header" id="heading18">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse18' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse18')}
                      aria-expanded={openAccordion === 'collapse18'}
                      aria-controls="collapse18"
                    >
                      What are the main principles of data protection?
                    </button>
                  </h2>
                  <div
                    id="collapse18"
                    className={`accordion-collapse ${openAccordion === 'collapse18' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading18"
                  >
                    <div className="accordion-body">
                      <p>
                        The main principles include lawfulness, fairness, and transparency; purpose limitation; data minimization; accuracy; storage limitation; integrity and confidentiality; and accountability. These principles ensure that personal data is processed responsibly and securely.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="0.8s"
                >
                  <h2 className="accordion-header" id="heading19">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse19' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse19')}
                      aria-expanded={openAccordion === 'collapse19'}
                      aria-controls="collapse19"
                    >
                      How can I protect my personal data online?
                    </button>
                  </h2>
                  <div
                    id="collapse19"
                    className={`accordion-collapse ${openAccordion === 'collapse19' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading19"
                  >
                    <div className="accordion-body">
                      <p>
                        Protect your personal data by using strong, unique passwords, enabling two-factor authentication, being cautious about sharing information on social media, using secure websites (HTTPS), keeping software updated, using antivirus software, and being wary of phishing attempts and suspicious links.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
                {/* FAQ Item Start */}
                <div
                  className="accordion-item wow fadeInUp"
                  data-wow-delay="1s"
                >
                  <h2 className="accordion-header" id="heading20">
                    <button
                      className={`accordion-button ${openAccordion === 'collapse20' ? '' : 'collapsed'}`}
                      type="button"
                      onClick={() => handleAccordionToggle('collapse20')}
                      aria-expanded={openAccordion === 'collapse20'}
                      aria-controls="collapse20"
                    >
                      How is my data stored and secured?
                    </button>
                  </h2>
                  <div
                    id="collapse20"
                    className={`accordion-collapse ${openAccordion === 'collapse20' ? 'show' : 'collapse'}`}
                    aria-labelledby="heading20"
                  >
                    <div className="accordion-body">
                      <p>
                        Your data is stored using industry-standard encryption both in transit and at rest. We implement multiple layers of security including access controls, regular security audits, secure data centers, and compliance with international security standards. We also have strict data retention policies and secure deletion procedures.
                      </p>
                    </div>
                  </div>
                </div>
                {/* FAQ Item End */}
              </div>
              {/* FAQ Accordion End */}
            </div>
            {/* FAQs section End */}
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* Page Faqs End */}
</>


      <Footer />
    </>
  );
}
