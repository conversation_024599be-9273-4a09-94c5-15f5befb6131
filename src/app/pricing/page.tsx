'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import ScrollingTicker from '@/components/layout/ScrollingTicker';
import { useScriptInitialization } from '@/hooks/useScriptInitialization';

export default function Pricing() {
  const isScriptsInitialized = useScriptInitialization();
  const [openAccordion, setOpenAccordion] = useState<string | null>('collapse1');

  const handleAccordionToggle = (accordionId: string) => {
    setOpenAccordion(openAccordion === accordionId ? null : accordionId);
  };

  return (
    <>
      {/* Preloader Start */}
      <div className="preloader">
        <div className="loading-container">
          <div className="loading" />
          <div id="loading-icon">
            <Image src="/images/loader.svg" alt="Loading" width={50} height={50} />
          </div>
        </div>
      </div>
      {/* Preloader End */}



      <PageHeader 
        title="Pricing"
        titleHighlight="plans"
        breadcrumbs={[
          { label: 'home', href: '/' },
          { label: 'pricing', isActive: true }
        ]}
      />

      <ScrollingTicker />

      <>
  {/* Page Pricing Start */}
  <div className="page-pricing">
    <div className="container">
      <div className="row">
        <div className="col-lg-4 col-md-6">
          {/* Pricing Box Start */}
          <div className="pricing-item wow fadeInUp">
            {/* Pricing Header Start */}
            <div className="pricing-header section-title">
              <h3>Basic plan</h3>
              <h2>$29/month</h2>
              <p>
                * Advanced security features designed for efficiency &amp;
                reliability
              </p>
            </div>
            {/* Pricing Header End */}
            {/* Pricing Body Start */}
            <div className="pricing-body">
              <ul>
                <li>Energy-Efficient Infrastructure</li>
                <li>Energy-Efficient Infrastructure</li>
                <li>Energy-Efficient Infrastructure</li>
                <li>Energy-Efficient Infrastructure</li>
                <li>Energy-Efficient Infrastructure</li>
              </ul>
            </div>
            {/* Pricing Body End */}
            {/* Pricing Button Start */}
            <div className="pricing-btn">
              <a href="contact.html" className="btn-default">
                Start Free Trial
              </a>
            </div>
            {/* Pricing Button End */}
          </div>
          {/* Pricing Box End */}
        </div>
        <div className="col-lg-4 col-md-6">
          {/* Pricing Box Start */}
          <div
            className="pricing-item highlighted-box wow fadeInUp"
            data-wow-delay="0.2s"
          >
            {/* Pricing Header Start */}
            <div className="pricing-header section-title">
              <h3>Popular Plan</h3>
              <h2>$89/month</h2>
              <p>
                * Advanced security features designed for efficiency &amp;
                reliability
              </p>
            </div>
            {/* Pricing Header End */}
            {/* Pricing Body Start */}
            <div className="pricing-body">
              <ul>
                <li>Energy-Efficient Infrastructure</li>
                <li>Energy-Efficient Infrastructure</li>
                <li>Energy-Efficient Infrastructure</li>
                <li>Energy-Efficient Infrastructure</li>
                <li>Energy-Efficient Infrastructure</li>
              </ul>
            </div>
            {/* Pricing Body End */}
            {/* Pricing Button Start */}
            <div className="pricing-btn">
              <a href="contact.html" className="btn-default">
                Start Free Trial
              </a>
            </div>
            {/* Pricing Button End */}
          </div>
          {/* Pricing Box End */}
        </div>
        <div className="col-lg-4 col-md-6">
          {/* Pricing Box Start */}
          <div className="pricing-item wow fadeInUp" data-wow-delay="0.4s">
            {/* Pricing Header Start */}
            <div className="pricing-header section-title">
              <h3>Pro plan</h3>
              <h2>$129/month</h2>
              <p>
                * Advanced security features designed for efficiency &amp;
                reliability
              </p>
            </div>
            {/* Pricing Header End */}
            {/* Pricing Body Start */}
            <div className="pricing-body">
              <ul>
                <li>Energy-Efficient Infrastructure</li>
                <li>Energy-Efficient Infrastructure</li>
                <li>Energy-Efficient Infrastructure</li>
                <li>Energy-Efficient Infrastructure</li>
                <li>Energy-Efficient Infrastructure</li>
              </ul>
            </div>
            {/* Pricing Body End */}
            {/* Pricing Button Start */}
            <div className="pricing-btn">
              <a href="contact.html" className="btn-default">
                Start Free Trial
              </a>
            </div>
            {/* Pricing Button End */}
          </div>
          {/* Pricing Box End */}
        </div>
        <div className="col-lg-12">
          {/* Pricing Benifit List Start */}
          <div
            className="pricing-benefit-list wow fadeInUp"
            data-wow-delay="0.6s"
          >
            <ul>
              <li>
                <img src="/images/icon-pricing-benefit-1.svg" alt="" />
                Get 30 day free trial
              </li>
              <li>
                <img src="/images/icon-pricing-benefit-2.svg" alt="" />
                No any hidden fees pay
              </li>
              <li>
                <img src="/images/icon-pricing-benefit-3.svg" alt="" />
                You can cancel anytime{" "}
              </li>
            </ul>
          </div>
          {/* Pricing Benifit List End */}
        </div>
      </div>
    </div>
  </div>
  {/* Page Pricing End */}
  {/* Our Testimonial Section Start */}
  <div className="our-testimonial">
    <div className="container">
      <div className="row section-row align-items-center">
        <div className="col-lg-6">
          {/* Section Title Start */}
          <div className="section-title">
            <h3 className="wow fadeInUp">our testimonials</h3>
            <h2
              className="wow fadeInUp"
              data-wow-delay="0.2s"
              data-cursor="-opaque"
            >
              Our clients speak real results <span>real protection</span>
            </h2>
          </div>
          {/* Section Title End */}
        </div>
        <div className="col-lg-6">
          {/* Customer Review Box Start */}
          <div className="customer-review-box testimonial-customer-box">
            {/* Customer Review Images Start */}
            <div className="customer-review-images">
              {/* Customer Image Start */}
              <div className="customer-image reveal">
                <figure className="image-anime">
                  <img src="/images/author-1.jpg" alt="" />
                </figure>
              </div>
              {/* Customer Image End */}
              {/* Customer Image Start */}
              <div className="customer-image reveal">
                <figure className="image-anime">
                  <img src="/images/author-2.jpg" alt="" />
                </figure>
              </div>
              {/* Customer Image End */}
              {/* Customer Image Start */}
              <div className="customer-image reveal">
                <figure className="image-anime">
                  <img src="/images/author-3.jpg" alt="" />
                </figure>
              </div>
              {/* Customer Image End */}
            </div>
            {/* Customer Review Images End */}
            {/* Customer Review Box Content Start */}
            <div className="customer-review-content">
              <p>More Than 1K+ trusted customers</p>
            </div>
            {/* Customer Review Box Content End */}
          </div>
          {/* Customer Review Box End */}
        </div>
      </div>
      <div className="row align-items-center">
        <div className="col-lg-4">
          {/* Testimonial Review Box Start */}
          <div className="testimonial-review-box">
            {/* Testimonial Review Header Start */}
            <div className="testimonial-review-header">
              <h2>
                <span className="counter">4.9</span>
              </h2>
              <div className="testimonial-rating">
                <i className="fa-solid fa-star" />
                <i className="fa-solid fa-star" />
                <i className="fa-solid fa-star" />
                <i className="fa-solid fa-star" />
                <i className="fa-solid fa-star" />
              </div>
              <p>
                (<span className="counter">40</span>+ Reviews)
              </p>
            </div>
            {/* Testimonial Review Header End */}
            {/* Testimonial Review Image Start */}
            <div className="customer-review-images">
              <div className="customer-image">
                <figure className="image-anime reveal">
                  <img src="/images/author-1.jpg" alt="" />
                </figure>
              </div>
              <div className="customer-image">
                <figure className="image-anime reveal">
                  <img src="/images/author-2.jpg" alt="" />
                </figure>
              </div>
              <div className="customer-image">
                <figure className="image-anime reveal">
                  <img src="/images/author-3.jpg" alt="" />
                </figure>
              </div>
              <div className="customer-image">
                <figure className="image-anime reveal">
                  <img src="/images/author-4.jpg" alt="" />
                </figure>
              </div>
              <div className="customer-image">
                <figure className="image-anime reveal">
                  <img src="/images/author-5.jpg" alt="" />
                </figure>
              </div>
            </div>
            {/* Testimonial Review Image End */}
            {/* Testimonial Review Content Start */}
            <div className="testimonial-review-content wow fadeInUp">
              <h3>Customer experiences that speak for themselves</h3>
            </div>
            {/* Testimonial Review Content End */}
          </div>
          {/* Testimonial Review Box End */}
        </div>
        <div className="col-lg-8">
          {/* Testimonial Slider Start */}
          <div className="testimonial-slider">
            <div className="swiper">
              <div className="swiper-wrapper" data-cursor-text="Drag">
                {/* Testimonial Slide Start */}
                <div className="swiper-slide">
                  <div className="testimonial-item">
                    <div className="testimonial-company-logo">
                      <img src="/images/icon-testimonial-logo.svg" alt="" />
                    </div>
                    <div className="testimonial-content">
                      <p>
                        The team transformed our brand's online presence with
                        creativity &amp; precision. The results exceeded our
                        expectations! Their digital marketing strategies helped
                        us reach a broader audience &amp; significantly boosted
                        our sales.
                      </p>
                    </div>
                    <div className="testimonial-body">
                      <div className="author-image">
                        <figure className="image-anime">
                          <img src="/images/author-1.jpg" alt="" />
                        </figure>
                      </div>
                      <div className="author-content">
                        <h3>Sarah Mitchell</h3>
                        <p>Marketing Director</p>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Testimonial Slide End */}
                {/* Testimonial Slide Start */}
                <div className="swiper-slide">
                  <div className="testimonial-item">
                    <div className="testimonial-company-logo">
                      <img src="/images/icon-testimonial-logo.svg" alt="" />
                    </div>
                    <div className="testimonial-content">
                      <p>
                        The team transformed our brand's online presence with
                        creativity &amp; precision. The results exceeded our
                        expectations! Their digital marketing strategies helped
                        us reach a broader audience &amp; significantly boosted
                        our sales.
                      </p>
                    </div>
                    <div className="testimonial-body">
                      <div className="author-image">
                        <figure className="image-anime">
                          <img src="/images/author-2.jpg" alt="" />
                        </figure>
                      </div>
                      <div className="author-content">
                        <h3>Ronald richards</h3>
                        <p>finance manager</p>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Testimonial Slide End */}
              </div>
              <div className="testimonial-btn">
                <div className="testimonial-button-prev" />
                <div className="testimonial-button-next" />
              </div>
            </div>
          </div>
          {/* Testimonial Slider End */}
        </div>
      </div>
    </div>
  </div>
  {/* Our Testimonial Section End */}
  {/* Our Faqs Section Start */}
  <div className="our-faqs">
    <div className="container">
      <div className="row">
        <div className="col-lg-6">
          {/* Faqs Content Start */}
          <div className="faqs-content">
            {/* Section Title Start */}
            <div className="section-title">
              <h3 className="wow fadeInUp">Frequently Asked Questions</h3>
              <h2
                className="wow fadeInUp"
                data-wow-delay="0.2s"
                data-cursor="-opaque"
              >
                Your complete FAQ guide to <span> cybersecurity solutions</span>
              </h2>
            </div>
            {/* Section Title End */}
            {/* Faqs Contact Box Start */}
            <div className="faqs-contact-box">
              {/* Faqs Contact Item Start */}
              <div
                className="faqs-contact-item wow fadeInUp"
                data-wow-delay="0.4s"
              >
                <div className="icon-box">
                  <img src="/images/sidebar-cta-img.svg" alt="" />
                </div>
                <div className="faqs-contact-item-content">
                  <h3>24/7 Customer Care</h3>
                  <p>
                    <a href="tel:659888695">(+1) 659-888-695</a>
                  </p>
                </div>
              </div>
              {/* Faqs Contact Item End */}
              {/* Faqs Contact Item Start */}
              <div
                className="faqs-contact-item wow fadeInUp"
                data-wow-delay="0.6s"
              >
                <div className="icon-box">
                  <img src="/images/icon-mail.svg" alt="" />
                </div>
                <div className="faqs-contact-item-content">
                  <h3>Technical Issues</h3>
                  <p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                  </p>
                </div>
              </div>
              {/* Faqs Contact Item End */}
            </div>
            {/* Faqs Contact Box End */}
            {/* Faqs Image Start */}
            <div className="faqs-image">
              <figure className="image-anime reveal">
                <img src="/images/faqs-image.jpg" alt="" />
              </figure>
            </div>
            {/* Faqs Image End */}
          </div>
          {/* Faqs Content End */}
        </div>
        <div className="col-lg-6">
          {/* FAQ Accordion Start */}
          <div className="faq-accordion" id="faqaccordion">
            {/* FAQ Item Start */}
            <div
              className="accordion-item wow fadeInUp"
              data-wow-delay="0.2s"
            >
              <h2 className="accordion-header" id="heading1">
                <button
                  className={`accordion-button ${openAccordion === 'collapse1' ? '' : 'collapsed'}`}
                  type="button"
                  onClick={() => handleAccordionToggle('collapse1')}
                  aria-expanded={openAccordion === 'collapse1'}
                  aria-controls="collapse1"
                >
                  What is cybersecurity, and why is it important?
                </button>
              </h2>
              <div
                id="collapse1"
                className={`accordion-collapse ${openAccordion === 'collapse1' ? 'show' : 'collapse'}`}
                aria-labelledby="heading1"
              >
                <div className="accordion-body">
                  <p>
                    Cybersecurity is the practice of protecting systems, networks, and programs from digital attacks. These cyberattacks are usually aimed at accessing, changing, or destroying sensitive information, extorting money from users, or interrupting normal business processes. It's important because in today's interconnected world, everyone benefits from advanced cyber defense programs.
                  </p>
                </div>
              </div>
            </div>
            {/* FAQ Item End */}
            {/* FAQ Item Start */}
            <div
              className="accordion-item wow fadeInUp"
              data-wow-delay="0.4s"
            >
              <h2 className="accordion-header" id="heading2">
                <button
                  className={`accordion-button ${openAccordion === 'collapse2' ? '' : 'collapsed'}`}
                  type="button"
                  onClick={() => handleAccordionToggle('collapse2')}
                  aria-expanded={openAccordion === 'collapse2'}
                  aria-controls="collapse2"
                >
                  Do small businesses need cybersecurity?
                </button>
              </h2>
              <div
                id="collapse2"
                className={`accordion-collapse ${openAccordion === 'collapse2' ? 'show' : 'collapse'}`}
                aria-labelledby="heading2"
              >
                <div className="accordion-body">
                  <p>
                    Yes, small businesses need cybersecurity just as much as larger enterprises. Cybercriminals often target smaller businesses because they may have weaker security defenses. A data breach or cyber attack can lead to financial loss, reputational damage, and legal consequences.
                  </p>
                </div>
              </div>
            </div>
            {/* FAQ Item End */}
            {/* FAQ Item Start */}
            <div
              className="accordion-item wow fadeInUp"
              data-wow-delay="0.6s"
            >
              <h2 className="accordion-header" id="heading3">
                <button
                  className={`accordion-button ${openAccordion === 'collapse3' ? '' : 'collapsed'}`}
                  type="button"
                  onClick={() => handleAccordionToggle('collapse3')}
                  aria-expanded={openAccordion === 'collapse3'}
                  aria-controls="collapse3"
                >
                  What is ransomware, and how can I protect against it?
                </button>
              </h2>
              <div
                id="collapse3"
                className={`accordion-collapse ${openAccordion === 'collapse3' ? 'show' : 'collapse'}`}
                aria-labelledby="heading3"
              >
                <div className="accordion-body">
                  <p>
                    Ransomware is malicious software that encrypts your files and demands payment to restore access. To protect against it, keep software updated, use strong passwords, enable two-factor authentication, regularly backup data, and be cautious of suspicious emails and links.
                  </p>
                </div>
              </div>
            </div>
            {/* FAQ Item End */}
            {/* FAQ Item Start */}
            <div
              className="accordion-item wow fadeInUp"
              data-wow-delay="0.8s"
            >
              <h2 className="accordion-header" id="heading4">
                <button
                  className={`accordion-button ${openAccordion === 'collapse4' ? '' : 'collapsed'}`}
                  type="button"
                  onClick={() => handleAccordionToggle('collapse4')}
                  aria-expanded={openAccordion === 'collapse4'}
                  aria-controls="collapse4"
                >
                  How do I know if my business has been hacked?
                </button>
              </h2>
              <div
                id="collapse4"
                className={`accordion-collapse ${openAccordion === 'collapse4' ? 'show' : 'collapse'}`}
                aria-labelledby="heading4"
              >
                <div className="accordion-body">
                  <p>
                    Signs of a hack include unusual account activity, unexpected password changes, slow system performance, pop-up messages, disabled security software, and unauthorized transactions. If you suspect a breach, immediately disconnect from the internet and contact cybersecurity professionals.
                  </p>
                </div>
              </div>
            </div>
            {/* FAQ Item End */}
            {/* FAQ Item Start */}
            <div
              className="accordion-item wow fadeInUp"
              data-wow-delay="1s"
            >
              <h2 className="accordion-header" id="heading5">
                <button
                  className={`accordion-button ${openAccordion === 'collapse5' ? '' : 'collapsed'}`}
                  type="button"
                  onClick={() => handleAccordionToggle('collapse5')}
                  aria-expanded={openAccordion === 'collapse5'}
                  aria-controls="collapse5"
                >
                  How often should I update my cybersecurity?
                </button>
              </h2>
              <div
                id="collapse5"
                className={`accordion-collapse ${openAccordion === 'collapse5' ? 'show' : 'collapse'}`}
                aria-labelledby="heading5"
              >
                <div className="accordion-body">
                  <p>
                    Cybersecurity should be updated continuously. Software updates should be applied as soon as they're available, security policies should be reviewed quarterly, and comprehensive security audits should be conducted annually. Stay informed about emerging threats and adjust your security measures accordingly.
                  </p>
                </div>
              </div>
            </div>
            {/* FAQ Item End */}
          </div>
          {/* FAQ Accordion End */}
        </div>
      </div>
    </div>
  </div>
  {/* Our Faqs Section End */}
</>


      <Footer />
    </>
  );
}
