'use client';

import { useEffect } from 'react';
import { use } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import ScrollingTicker from '@/components/layout/ScrollingTicker';
import { useScriptInitialization } from '@/hooks/useScriptInitialization';
import { getTeamMemberBySlug, TeamMember } from '@/data/teamMembers';
import { highlightTeamMemberName } from '@/utils/titleHighlighting';

interface TeamMemberPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default function TeamMemberPage({ params }: TeamMemberPageProps) {
  const isScriptsInitialized = useScriptInitialization();
  const { slug } = use(params);
  
  // Reset magic cursor state when page loads
  useEffect(() => {
    const resetCursorState = () => {
      if (typeof window !== 'undefined' && (window as any).cursor) {
        (window as any).cursor.removeState('-text');
        (window as any).cursor.removeText();
      }
    };
    
    // Reset immediately and after a short delay
    resetCursorState();
    setTimeout(resetCursorState, 100);
    setTimeout(resetCursorState, 500);
  }, []);
  
  // Get team member data based on slug
  const teamMember = getTeamMemberBySlug(slug);
  
  // If team member not found, show 404
  if (!teamMember) {
    return (
      <div className="container mt-5">
        <h1>Team member not found</h1>
        <Link href="/team">Back to team</Link>
      </div>
    );
  }

  return (
    <>
      {/* Preloader Start */}
      <div className="preloader">
        <div className="loading-container">
          <div className="loading" />
          <div id="loading-icon">
            <Image src="/images/loader.svg" alt="Loading" width={50} height={50} />
          </div>
        </div>
      </div>
      {/* Preloader End */}

      <PageHeader 
        title={highlightTeamMemberName(teamMember.name).title}
        titleHighlight={highlightTeamMemberName(teamMember.name).titleHighlight}
        breadcrumbs={[
          { label: 'home', href: '/' },
          { label: 'team', href: '/team' },
          { label: teamMember.name, isActive: true }
        ]}
      />
      <ScrollingTicker />

      {/* Page Team Single Start */}
      <div className="page-team-single">
        <div className="container">
          <div className="row">
            <div className="col-lg-4">
              {/* Page Single Sidebar Start */}
              <div className="page-single-sidebar">
                {/* Team Single Image Start */}
                <div className="team-single-image wow fadeInUp">
                  <figure className="image-anime">
                    <Image src={teamMember.image} alt={teamMember.name} width={400} height={500} />
                  </figure>
                  {/* Member Social List Start */}
                  <div className="member-social-list">
                    <ul>
                      {teamMember.social.dribbble && (
                        <li>
                          <a href={teamMember.social.dribbble}>
                            <i className="fa-brands fa-dribbble" />
                          </a>
                        </li>
                      )}
                      {teamMember.social.facebook && (
                        <li>
                          <a href={teamMember.social.facebook}>
                            <i className="fa-brands fa-facebook-f" />
                          </a>
                        </li>
                      )}
                      {teamMember.social.instagram && (
                        <li>
                          <a href={teamMember.social.instagram}>
                            <i className="fa-brands fa-instagram" />
                          </a>
                        </li>
                      )}
                      {teamMember.social.linkedin && (
                        <li>
                          <a href={teamMember.social.linkedin}>
                            <i className="fa-brands fa-linkedin-in" />
                          </a>
                        </li>
                      )}
                    </ul>
                  </div>
                  {/* Member Social List End */}
                </div>
                {/* Team Single Image End */}
              </div>
              {/* Page Single Sidebar End */}
            </div>
            <div className="col-lg-8">
              {/* Team Single Content Start */}
              <div className="team-single-content">
                {/* Team Member Info Start */}
                <div className="team-member-info">
                  {/* Section Title Start */}
                  <div className="section-title">
                    <h3 className="wow fadeInUp">{teamMember.position}</h3>
                    <h2
                      className="wow fadeInUp"
                      data-wow-delay="0.2s"
                      data-cursor="-opaque"
                    >
                      About <span>{teamMember.name.split(' ')[0]}</span>
                    </h2>
                    <p className="wow fadeInUp" data-wow-delay="0.4s">
                      {teamMember.bio}
                    </p>
                  </div>
                  {/* Section Title End */}
                  {/* Team Member Body Start */}
                  <div className="team-member-body">
                    {/* Team Member Body List Start */}
                    <div
                      className="team-member-body-list wow fadeInUp"
                      data-wow-delay="0.6s"
                    >
                      <ul>
                        {teamMember.achievements.map((achievement, index) => (
                          <li key={index}>{achievement}</li>
                        ))}
                      </ul>
                    </div>
                    {/* Team Member Body List End */}
                    {/* Team Contact List Start */}
                    <div
                      className="team-contact-list wow fadeInUp"
                      data-wow-delay="0.8s"
                    >
                      {/* Footer Contact Item Start */}
                      <div className="footer-contact-item">
                        <div className="icon-box">
                          <i className="fa-solid fa-phone" />
                        </div>
                        <div className="footer-contact-content">
                          <p>
                            <a href={`tel:${teamMember.contact.phone}`}>{teamMember.contact.phone}</a>
                          </p>
                        </div>
                      </div>
                      {/* Footer Contact Item End */}
                      {/* Footer Contact Item Start */}
                      <div className="footer-contact-item">
                        <div className="icon-box">
                          <i className="fa-solid fa-envelope" />
                        </div>
                        <div className="footer-contact-content">
                          <p>
                            <a href={`mailto:${teamMember.contact.email}`}>
                              {teamMember.contact.email}
                            </a>
                          </p>
                        </div>
                      </div>
                      {/* Footer Contact Item End */}
                    </div>
                    {/* Team Contact List End */}
                  </div>
                  {/* Team Member Body End */}
                </div>
                {/* Team Member Info End */}
                {/* Team Member Skills Start */}
                <div className="team-member-skills">
                  {/* Section Title Start */}
                  <div className="section-title">
                    <h2 className="wow fadeInUp" data-cursor="-opaque">
                      Achievement &amp; <span>recognition</span>
                    </h2>
                  </div>
                  {/* Section Title End */}
                  {/* Team Skills List Start */}
                  <div className="member-skills-list">
                    {teamMember.skills.map((skill, index) => (
                      <div key={index} className="skills-progress-bar">
                        {/* Skill Item Start */}
                        <div className="skillbar" data-percent={`${skill.percentage}%`}>
                          <div className="skill-data">
                            <div className="skill-title">
                              {skill.name}
                            </div>
                            <div className="skill-no">{skill.percentage}%</div>
                          </div>
                          <div className="skill-progress">
                            <div className="count-bar" />
                          </div>
                        </div>
                        {/* Skill Item End */}
                      </div>
                    ))}
                  </div>
                  {/* Team Skills List End */}
                </div>
                {/* Team Member Skill End */}
                {/* Team Member Qualification Start */}
                <div className="team-member-qualification">
                  {/* Section Title Start */}
                  <div className="section-title">
                    <h2 className="wow fadeInUp" data-cursor="-opaque">
                      Qualification <span>&amp; experience</span>
                    </h2>
                  </div>
                  {/* Section Title End */}
                  {/* Team Member List Start */}
                  <div
                    className="team-member-list wow fadeInUp"
                    data-wow-delay="0.2s"
                  >
                    <ul>
                      {teamMember.qualifications.map((qualification, index) => (
                        <li key={index}>{qualification}</li>
                      ))}
                    </ul>
                  </div>
                  {/* Team Member List End */}
                </div>
                {/* Team Member Qualification End */}
                {/* Contact Us Form Start */}
                <div className="contact-us-form team-contact-form">
                  {/* Section Title Start */}
                  <div className="section-title">
                    <h2 className="wow fadeInUp" data-cursor="-opaque">
                      Get in <span>touch now!</span>
                    </h2>
                  </div>
                  {/* Section Title End */}
                  {/* Contact Form Start */}
                  <div className="contact-form">
                    <form
                      id="contactForm"
                      action="#"
                      method="POST"
                      data-toggle="validator"
                      className="wow fadeInUp"
                      data-wow-delay="0.2s"
                    >
                      <div className="row">
                        <div className="form-group col-md-6 mb-4">
                          <input
                            type="text"
                            name="fname"
                            className="form-control"
                            id="fname"
                            placeholder="Enter Your First Name"
                            required
                          />
                          <div className="help-block with-errors" />
                        </div>
                        <div className="form-group col-md-6 mb-4">
                          <input
                            type="text"
                            name="lname"
                            className="form-control"
                            id="lname"
                            placeholder="Enter Your Last Name"
                            required
                          />
                          <div className="help-block with-errors" />
                        </div>
                        <div className="form-group col-md-6 mb-4">
                          <input
                            type="text"
                            name="phone"
                            className="form-control"
                            id="phone"
                            placeholder="Enter Your Phone Number"
                            required
                          />
                          <div className="help-block with-errors" />
                        </div>
                        <div className="form-group col-md-6 mb-4">
                          <input
                            type="email"
                            name="email"
                            className="form-control"
                            id="email"
                            placeholder="Enter Your Email"
                            required
                          />
                          <div className="help-block with-errors" />
                        </div>
                        <div className="form-group col-md-12 mb-5">
                          <textarea
                            name="message"
                            className="form-control"
                            id="message"
                            rows={6}
                            placeholder="Enter Your Message"
                            defaultValue=""
                          />
                          <div className="help-block with-errors" />
                        </div>
                        <div className="col-md-12">
                          <button type="submit" className="btn-default">
                            Send Message
                          </button>
                          <div id="msgSubmit" className="h3 hidden" />
                        </div>
                      </div>
                    </form>
                  </div>
                  {/* Contact Form End */}
                </div>
                {/* Contact Us Form End */}
              </div>
              {/* Team Single Content End */}
            </div>
          </div>
        </div>
      </div>
      {/* Page Team Single End */}

      <Footer />
    </>
  );
}
