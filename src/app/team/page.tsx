'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import ScrollingTicker from '@/components/layout/ScrollingTicker';
import { useScriptInitialization } from '@/hooks/useScriptInitialization';
import { getAllTeamMembers } from '@/data/teamMembers';

export default function Team() {
  const isScriptsInitialized = useScriptInitialization();
  const teamMembers = getAllTeamMembers();

  return (
    <>
      {/* Preloader Start */}
      <div className="preloader">
        <div className="loading-container">
          <div className="loading" />
          <div id="loading-icon">
            <Image src="/images/loader.svg" alt="Loading" width={50} height={50} />
          </div>
        </div>
      </div>
      {/* Preloader End */}

      <PageHeader 
        title="Our"
        titleHighlight="team"
        breadcrumbs={[
          { label: 'home', href: '/' },
          { label: 'our team', isActive: true }
        ]}
      />

      <ScrollingTicker />

      {/* Page Team Start */}
      <div className="page-team">
        <div className="container">
          <div className="row">
            {teamMembers.map((member, index) => (
              <div key={member.slug} className="col-lg-3 col-md-6">
                {/* Team Member Item Start */}
                <div className="team-item wow fadeInUp" data-wow-delay={`${index * 0.2}s`}>
                  {/* team Image Start */}
                  <div className="team-image">
                    <Link href={`/team/${member.slug}`} className="image-anime" data-cursor-text="View">
                      <figure>
                        <Image src={member.image} alt={member.name} width={300} height={400} />
                      </figure>
                    </Link>
                  </div>
                  {/* team Image End */}
                  {/* Team Body Start */}
                  <div className="team-body">
                    {/* Team Content Start */}
                    <div className="team-content">
                      <h3>
                        <Link href={`/team/${member.slug}`} data-cursor-text="View">{member.name}</Link>
                      </h3>
                      <p>{member.position}</p>
                    </div>
                    {/* Team Content End */}
                    {/* Team Social List Start */}
                    <div className="team-social-list">
                      <ul>
                        {member.social.facebook && (
                          <li>
                            <a href={member.social.facebook}>
                              <i className="fa-brands fa-facebook-f" />
                            </a>
                          </li>
                        )}
                        {member.social.instagram && (
                          <li>
                            <a href={member.social.instagram}>
                              <i className="fa-brands fa-instagram" />
                            </a>
                          </li>
                        )}
                        {member.social.linkedin && (
                          <li>
                            <a href={member.social.linkedin}>
                              <i className="fa-brands fa-linkedin-in" />
                            </a>
                          </li>
                        )}
                        {member.social.twitter && (
                          <li>
                            <a href={member.social.twitter}>
                              <i className="fa-brands fa-twitter" />
                            </a>
                          </li>
                        )}
                        {member.social.dribbble && (
                          <li>
                            <a href={member.social.dribbble}>
                              <i className="fa-brands fa-dribbble" />
                            </a>
                          </li>
                        )}
                      </ul>
                    </div>
                    {/* Team Social List End */}
                  </div>
                  {/* Team Body End */}
                </div>
                {/* Team Member Item End */}
              </div>
            ))}
          </div>
        </div>
      </div>
      {/* Page Team End */}

      <Footer />
    </>
  );
}
