'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import ScrollingTicker from '@/components/layout/ScrollingTicker';
import { useScriptInitialization } from '@/hooks/useScriptInitialization';

export default function About() {
  const isScriptsInitialized = useScriptInitialization();
  const [openAccordion, setOpenAccordion] = useState<string | null>('collapse1');

  const handleAccordionToggle = (accordionId: string) => {
    setOpenAccordion(openAccordion === accordionId ? null : accordionId);
  };

  return (
    <>
      {/* Preloader Start */}
      <div className="preloader">
        <div className="loading-container">
          <div className="loading" />
          <div id="loading-icon">
            <Image src="/images/loader.svg" alt="Loading" width={50} height={50} />
          </div>
        </div>
      </div>
      {/* Preloader End */}


      <PageHeader 
        title=""
        titleHighlight="Testimonials"
        breadcrumbs={[
          { label: 'home', href: '/' },
          { label: 'Testimonials', isActive: true }
        ]}
      />

      <ScrollingTicker />

      <>
  {/* Page Testimonials Start */}
  <div className="page-testimonials">
    <div className="container">
      <div className="row">
        <div className="col-md-6">
          {/* Testimonial Item Start */}
          <div className="testimonial-item wow fadeInUp">
            <div className="testimonial-rating">
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
            </div>
            <div className="testimonial-content">
              <p>
                The team transformed our brand's online presence with creativity
                &amp; precision. The results exceeded our expectations! Their
                digital marketing strategies helped us reach a broader audience
                &amp; significantly boosted our sales.
              </p>
            </div>
            <div className="testimonial-body">
              <div className="testimonial-author-box">
                <div className="author-image">
                  <figure className="image-anime">
                    <img src="/images/author-1.jpg" alt="" />
                  </figure>
                </div>
                <div className="author-content">
                  <h3>Sarah Mitchell</h3>
                  <p>CEO</p>
                </div>
              </div>
              <div className="testimonial-company-logo">
                <img src="/images/icon-testimonial-logo.svg" alt="" />
              </div>
            </div>
          </div>
          {/* Testimonial Item End */}
        </div>
        <div className="col-md-6">
          {/* Testimonial Item Start */}
          <div className="testimonial-item wow fadeInUp" data-wow-delay="0.2s">
            <div className="testimonial-rating">
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
            </div>
            <div className="testimonial-content">
              <p>
                The team transformed our brand's online presence with creativity
                &amp; precision. The results exceeded our expectations! Their
                digital marketing strategies helped us reach a broader audience
                &amp; significantly boosted our sales.
              </p>
            </div>
            <div className="testimonial-body">
              <div className="testimonial-author-box">
                <div className="author-image">
                  <figure className="image-anime">
                    <img src="/images/author-2.jpg" alt="" />
                  </figure>
                </div>
                <div className="author-content">
                  <h3>Robert Fox</h3>
                  <p>HR Consultant</p>
                </div>
              </div>
              <div className="testimonial-company-logo">
                <img src="/images/icon-testimonial-logo.svg" alt="" />
              </div>
            </div>
          </div>
          {/* Testimonial Item End */}
        </div>
        <div className="col-md-6">
          {/* Testimonial Item Start */}
          <div className="testimonial-item wow fadeInUp" data-wow-delay="0.4s">
            <div className="testimonial-rating">
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
            </div>
            <div className="testimonial-content">
              <p>
                The team transformed our brand's online presence with creativity
                &amp; precision. The results exceeded our expectations! Their
                digital marketing strategies helped us reach a broader audience
                &amp; significantly boosted our sales.
              </p>
            </div>
            <div className="testimonial-body">
              <div className="testimonial-author-box">
                <div className="author-image">
                  <figure className="image-anime">
                    <img src="/images/author-3.jpg" alt="" />
                  </figure>
                </div>
                <div className="author-content">
                  <h3>Bessie Cooper</h3>
                  <p>HR Specialist</p>
                </div>
              </div>
              <div className="testimonial-company-logo">
                <img src="/images/icon-testimonial-logo.svg" alt="" />
              </div>
            </div>
          </div>
          {/* Testimonial Item End */}
        </div>
        <div className="col-md-6">
          {/* Testimonial Item Start */}
          <div className="testimonial-item wow fadeInUp" data-wow-delay="0.6s">
            <div className="testimonial-rating">
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
            </div>
            <div className="testimonial-content">
              <p>
                The team transformed our brand's online presence with creativity
                &amp; precision. The results exceeded our expectations! Their
                digital marketing strategies helped us reach a broader audience
                &amp; significantly boosted our sales.
              </p>
            </div>
            <div className="testimonial-body">
              <div className="testimonial-author-box">
                <div className="author-image">
                  <figure className="image-anime">
                    <img src="/images/author-4.jpg" alt="" />
                  </figure>
                </div>
                <div className="author-content">
                  <h3>Albert Flores</h3>
                  <p>Compliance officer</p>
                </div>
              </div>
              <div className="testimonial-company-logo">
                <img src="/images/icon-testimonial-logo.svg" alt="" />
              </div>
            </div>
          </div>
          {/* Testimonial Item End */}
        </div>
        <div className="col-md-6">
          {/* Testimonial Item Start */}
          <div className="testimonial-item wow fadeInUp" data-wow-delay="0.8s">
            <div className="testimonial-rating">
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
            </div>
            <div className="testimonial-content">
              <p>
                The team transformed our brand's online presence with creativity
                &amp; precision. The results exceeded our expectations! Their
                digital marketing strategies helped us reach a broader audience
                &amp; significantly boosted our sales.
              </p>
            </div>
            <div className="testimonial-body">
              <div className="testimonial-author-box">
                <div className="author-image">
                  <figure className="image-anime">
                    <img src="/images/author-5.jpg" alt="" />
                  </figure>
                </div>
                <div className="author-content">
                  <h3>Wade Warren</h3>
                  <p>managing director</p>
                </div>
              </div>
              <div className="testimonial-company-logo">
                <img src="/images/icon-testimonial-logo.svg" alt="" />
              </div>
            </div>
          </div>
          {/* Testimonial Item End */}
        </div>
        <div className="col-md-6">
          {/* Testimonial Item Start */}
          <div className="testimonial-item wow fadeInUp" data-wow-delay="1s">
            <div className="testimonial-rating">
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
              <i className="fa-solid fa-star" />
            </div>
            <div className="testimonial-content">
              <p>
                The team transformed our brand's online presence with creativity
                &amp; precision. The results exceeded our expectations! Their
                digital marketing strategies helped us reach a broader audience
                &amp; significantly boosted our sales.
              </p>
            </div>
            <div className="testimonial-body">
              <div className="testimonial-author-box">
                <div className="author-image">
                  <figure className="image-anime">
                    <img src="/images/author-6.jpg" alt="" />
                  </figure>
                </div>
                <div className="author-content">
                  <h3>Ronald Richards</h3>
                  <p>finance manager</p>
                </div>
              </div>
              <div className="testimonial-company-logo">
                <img src="/images/icon-testimonial-logo.svg" alt="" />
              </div>
            </div>
          </div>
          {/* Testimonial Item End */}
        </div>
      </div>
    </div>
  </div>
  {/* Page Testimonials End */}
  {/* Why Choose Us Section Start */}
  <div className="why-choose-us">
    <div className="container">
      <div className="row">
        <div className="col-lg-12">
          {/* Why Choose Box Start */}
          <div className="why-choose-box">
            <div className="row section-row align-items-center">
              <div className="col-lg-7 col-md-9">
                {/* Section Title Start */}
                <div className="section-title">
                  <h3 className="wow fadeInUp">Why Choose Us</h3>
                  <h2
                    className="wow fadeInUp"
                    data-wow-delay="0.2s"
                    data-cursor="-opaque"
                  >
                    Trusted experts committed to securing{" "}
                    <span>your digital</span>
                  </h2>
                </div>
                {/* Section Title End */}
              </div>
              <div className="col-lg-5 col-md-3">
                {/* About Us Circle Start */}
                <div className="about-us-circle">
                  <a href="about.html">
                    <img src="/images/about-us-circle.png" alt="" />
                  </a>
                </div>
                {/* About Us Circle End */}
              </div>
            </div>
            {/* Why Choose Item List Start */}
            <div className="why-choose-item-list">
              {/* Why Choose Item Start */}
              <div className="why-choose-item wow fadeInUp">
                <div className="icon-box">
                  <img src="/images/icon-why-choose-item-1.svg" alt="" />
                </div>
                <div className="why-choose-item-content">
                  <h3>Tailored Security Solutions</h3>
                  <p>
                    Every business is unique, and so are our solutions. We
                    customize strategy fit your specific needs.
                  </p>
                </div>
              </div>
              {/* Why Choose Item End */}
              {/* Why Choose Item Start */}
              <div
                className="why-choose-item wow fadeInUp"
                data-wow-delay="0.2s"
              >
                <div className="icon-box">
                  <img src="/images/icon-why-choose-item-2.svg" alt="" />
                </div>
                <div className="why-choose-item-content">
                  <h3>Advanced Technology</h3>
                  <p>
                    We use the latest tools and cutting-edge technologies to
                    safeguard your digital environment.
                  </p>
                </div>
              </div>
              {/* Why Choose Item End */}
              {/* Why Choose Item Start */}
              <div
                className="why-choose-item wow fadeInUp"
                data-wow-delay="0.4s"
              >
                <div className="icon-box">
                  <img src="/images/icon-why-choose-item-3.svg" alt="" />
                </div>
                <div className="why-choose-item-content">
                  <h3>Real-Time Threat Detection</h3>
                  <p>
                    Stay ahead of attackers with pro active threat detection and
                    response systems that operate 24/7.
                  </p>
                </div>
              </div>
              {/* Why Choose Item End */}
            </div>
            {/* Why Choose Item List End */}
            {/* Section Footer Text Start */}
            <div
              className="section-footer-text wow fadeInUp"
              data-wow-delay="0.6s"
            >
              <p>
                <span>Free</span>Let's make something great work together. -{" "}
                <a href="contact.html">Get Free Quote</a>
              </p>
            </div>
            {/* Section Footer Text End */}
          </div>
          {/* Why Choose Box End */}
        </div>
      </div>
    </div>
  </div>
  {/* Why Choose Us Section End */}
  {/* Our Faqs Section Start */}
  <div className="our-faqs">
    <div className="container">
      <div className="row">
        <div className="col-lg-6">
          {/* Faqs Content Start */}
          <div className="faqs-content">
            {/* Section Title Start */}
            <div className="section-title">
              <h3 className="wow fadeInUp">Frequently Asked Questions</h3>
              <h2
                className="wow fadeInUp"
                data-wow-delay="0.2s"
                data-cursor="-opaque"
              >
                Your complete FAQ guide to <span> cybersecurity solutions</span>
              </h2>
            </div>
            {/* Section Title End */}
            {/* Faqs Contact Box Start */}
            <div className="faqs-contact-box">
              {/* Faqs Contact Item Start */}
              <div
                className="faqs-contact-item wow fadeInUp"
                data-wow-delay="0.4s"
              >
                <div className="icon-box">
                  <img src="/images/sidebar-cta-img.svg" alt="" />
                </div>
                <div className="faqs-contact-item-content">
                  <h3>24/7 Customer Care</h3>
                  <p>
                    <a href="tel:659888695">(+1) 659-888-695</a>
                  </p>
                </div>
              </div>
              {/* Faqs Contact Item End */}
              {/* Faqs Contact Item Start */}
              <div
                className="faqs-contact-item wow fadeInUp"
                data-wow-delay="0.6s"
              >
                <div className="icon-box">
                  <img src="/images/icon-mail.svg" alt="" />
                </div>
                <div className="faqs-contact-item-content">
                  <h3>Technical Issues</h3>
                  <p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                  </p>
                </div>
              </div>
              {/* Faqs Contact Item End */}
            </div>
            {/* Faqs Contact Box End */}
            {/* Faqs Image Start */}
            <div className="faqs-image">
              <figure className="image-anime reveal">
                <img src="/images/faqs-image.jpg" alt="" />
              </figure>
            </div>
            {/* Faqs Image End */}
          </div>
          {/* Faqs Content End */}
        </div>
        <div className="col-lg-6">
          {/* FAQs section start */}
          <div className="faq-accordion page-faq-accordion" id="faq1">
            <div className="section-title">
              <h2 className="wow fadeInUp" data-cursor="-opaque">
                General <span>questions</span>
              </h2>
            </div>
            {/* FAQ Accordion Start */}
            <div className="faq-accordion" id="accordion">
              {/* FAQ Item Start */}
              <div
                className="accordion-item wow fadeInUp"
                data-wow-delay="0.2s"
              >
                <h2 className="accordion-header" id="heading1">
                  <button
                    className={`accordion-button ${openAccordion === 'collapse1' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handleAccordionToggle('collapse1')}
                    aria-expanded={openAccordion === 'collapse1'}
                    aria-controls="collapse1"
                  >
                    What is cybersecurity, and why is it important?
                  </button>
                </h2>
                <div
                  id="collapse1"
                  className={`accordion-collapse ${openAccordion === 'collapse1' ? 'show' : 'collapse'}`}
                  aria-labelledby="heading1"
                >
                  <div className="accordion-body">
                    <p>
                      Cybersecurity is the practice of protecting systems, networks, and programs from digital attacks. These cyberattacks are usually aimed at accessing, changing, or destroying sensitive information, extorting money from users, or interrupting normal business processes. It's important because in today's interconnected world, everyone benefits from advanced cyber defense programs.
                    </p>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
              {/* FAQ Item Start */}
              <div
                className="accordion-item wow fadeInUp"
                data-wow-delay="0.4s"
              >
                <h2 className="accordion-header" id="heading2">
                  <button
                    className={`accordion-button ${openAccordion === 'collapse2' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handleAccordionToggle('collapse2')}
                    aria-expanded={openAccordion === 'collapse2'}
                    aria-controls="collapse2"
                  >
                    Do small businesses need cybersecurity?
                  </button>
                </h2>
                <div
                  id="collapse2"
                  className={`accordion-collapse ${openAccordion === 'collapse2' ? 'show' : 'collapse'}`}
                  aria-labelledby="heading2"
                >
                  <div className="accordion-body">
                    <p>
                      Yes, small businesses need cybersecurity just as much as larger enterprises. Cybercriminals often target smaller businesses because they may have weaker security defenses. A data breach or cyber attack can lead to financial loss, reputational damage, and legal consequences.
                    </p>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
              {/* FAQ Item Start */}
              <div
                className="accordion-item wow fadeInUp"
                data-wow-delay="0.6s"
              >
                <h2 className="accordion-header" id="heading3">
                  <button
                    className={`accordion-button ${openAccordion === 'collapse3' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handleAccordionToggle('collapse3')}
                    aria-expanded={openAccordion === 'collapse3'}
                    aria-controls="collapse3"
                  >
                    What is ransomware, and how can I protect against it?
                  </button>
                </h2>
                <div
                  id="collapse3"
                  className={`accordion-collapse ${openAccordion === 'collapse3' ? 'show' : 'collapse'}`}
                  aria-labelledby="heading3"
                >
                  <div className="accordion-body">
                    <p>
                      Ransomware is malicious software that encrypts your files and demands payment to restore access. To protect against it, keep software updated, use strong passwords, enable two-factor authentication, regularly backup data, and be cautious of suspicious emails and links.
                    </p>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
              {/* FAQ Item Start */}
              <div
                className="accordion-item wow fadeInUp"
                data-wow-delay="0.8s"
              >
                <h2 className="accordion-header" id="heading4">
                  <button
                    className={`accordion-button ${openAccordion === 'collapse4' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handleAccordionToggle('collapse4')}
                    aria-expanded={openAccordion === 'collapse4'}
                    aria-controls="collapse4"
                  >
                    How do I know if my business has been hacked?
                  </button>
                </h2>
                <div
                  id="collapse4"
                  className={`accordion-collapse ${openAccordion === 'collapse4' ? 'show' : 'collapse'}`}
                  aria-labelledby="heading4"
                >
                  <div className="accordion-body">
                    <p>
                      Signs of a hack include unusual account activity, unexpected password changes, slow system performance, pop-up messages, disabled security software, and unauthorized transactions. If you suspect a breach, immediately disconnect from the internet and contact cybersecurity professionals.
                    </p>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
              {/* FAQ Item Start */}
              <div
                className="accordion-item wow fadeInUp"
                data-wow-delay="1s"
              >
                <h2 className="accordion-header" id="heading5">
                  <button
                    className={`accordion-button ${openAccordion === 'collapse5' ? '' : 'collapsed'}`}
                    type="button"
                    onClick={() => handleAccordionToggle('collapse5')}
                    aria-expanded={openAccordion === 'collapse5'}
                    aria-controls="collapse5"
                  >
                    How often should I update my cybersecurity?
                  </button>
                </h2>
                <div
                  id="collapse5"
                  className={`accordion-collapse ${openAccordion === 'collapse5' ? 'show' : 'collapse'}`}
                  aria-labelledby="heading5"
                >
                  <div className="accordion-body">
                    <p>
                      Cybersecurity should be updated continuously. Software updates should be applied as soon as they're available, security policies should be reviewed quarterly, and comprehensive security audits should be conducted annually. Stay informed about emerging threats and adjust your security measures accordingly.
                    </p>
                  </div>
                </div>
              </div>
              {/* FAQ Item End */}
            </div>
            {/* FAQ Accordion End */}
          </div>
          {/* FAQs section End */}
        </div>
      </div>
    </div>
  </div>
  {/* Our Faqs Section End */}
</>


      <Footer />
    </>
  );
}
