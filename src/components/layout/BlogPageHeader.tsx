'use client';

import React from 'react';
import Link from 'next/link';

interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

interface BlogPageHeaderProps {
  title: string;
  titleHighlight?: string;
  breadcrumbs: BreadcrumbItem[];
}

export default function BlogPageHeader({ title, titleHighlight, breadcrumbs }: BlogPageHeaderProps) {
  return (
    <>
      {/* Page Header Start */}
      <div className="page-header">
        {/* Grid Lines Start */}
        <div className="grid-lines">
          <div className="grid-line-1" />
          <div className="grid-line-2" />
          <div className="grid-line-3" />
          <div className="grid-line-4" />
          <div className="grid-line-5" />
        </div>
        {/* Grid Lines End */}
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-12">
              {/* Page Header Box Start */}
              <div className="page-header-box">
                <div className="section-title">
                  <h1 className="wow fadeInUp">
                    {title} {titleHighlight && <span>{titleHighlight}</span>}
                  </h1>
                </div>
                <nav className="wow fadeInUp" data-wow-delay="0.2s">
                  <ol className="breadcrumb">
                    {breadcrumbs.map((item, index) => (
                      <li 
                        key={index} 
                        className={`breadcrumb-item ${item.isActive ? 'active' : ''}`}
                        aria-current={item.isActive ? 'page' : undefined}
                      >
                        {item.href ? (
                          <Link href={item.href}>{item.label}</Link>
                        ) : (
                          item.label
                        )}
                      </li>
                    ))}
                  </ol>
                </nav>
              </div>
              {/* Page Header Box End */}
            </div>
          </div>
        </div>
      </div>
      {/* Page Header End */}
    </>
  );
}
