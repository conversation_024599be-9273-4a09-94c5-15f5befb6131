import React from 'react';

const HomeImageHero: React.FC = () => {
  return (
    <>
      {/* Hero Section Start */}
      <div className="hero hero-bg-image parallaxie">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-12">
              {/* Hero Content Start */}
              <div className="hero-content">
                {/* Section Title Start */}
                <div className="section-title section-title-center">
                  <h3 className="wow fadeInUp">
                    Protect your business from cyber threat
                  </h3>
                  <h1
                    className="wow fadeInUp"
                    data-wow-delay="0.2s"
                    data-cursor="-opaque"
                  >
                    Advanced cybersecurity solutions tailored to{" "}
                    <span>keep you safe</span>
                  </h1>
                  <p className="wow fadeInUp" data-wow-delay="0.4s">
                    In a world where cyberattacks are becoming more sophisticated,
                    your business deserves the best protection. Our expert team
                    leverages cutting-edge technology.
                  </p>
                </div>
                {/* Section Title End */}
                {/* Hero Button Start */}
                <div className="hero-btn wow fadeInUp" data-wow-delay="0.6s">
                  <a href="contact.html" className="btn-default btn-highlighted">
                    Get Free Assessment
                  </a>
                  <a href="services.html" className="btn-default">
                    View our services
                  </a>
                </div>
                {/* Hero Button End */}
              </div>
              {/* Hero Content End */}
            </div>
          </div>
          <div className="row">
            <div className="col-lg-12">
              {/* Hero Company Slider Start */}
              <div className="hero-company-slider">
                <p>
                  We're Trusted by more than <span className="counter">1000</span>+
                  companies
                </p>
                <div className="swiper">
                  <div className="swiper-wrapper">
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-1.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-2.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-3.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-4.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-1.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-2.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-3.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                  </div>
                </div>
              </div>
              {/* Hero Company Slider End */}
            </div>
          </div>
        </div>
      </div>
      {/* Hero Section End */}
    </>
  );
};

export default HomeImageHero;
