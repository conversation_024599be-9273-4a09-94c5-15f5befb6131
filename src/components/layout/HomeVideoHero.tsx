'use client';

import React, { useEffect, useRef } from 'react';

const HomeVideoHero: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [videoStatus, setVideoStatus] = React.useState('loading');

  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      console.log('Video element found, attempting to play...');
      
      // Add event listeners for debugging
      video.addEventListener('loadstart', () => {
        console.log('Video loadstart');
        setVideoStatus('loading');
      });
      video.addEventListener('loadeddata', () => {
        console.log('Video loadeddata');
        setVideoStatus('loaded');
      });
      video.addEventListener('canplay', () => {
        console.log('Video canplay');
        setVideoStatus('canplay');
      });
      video.addEventListener('playing', () => {
        console.log('Video playing');
        setVideoStatus('playing');
      });
      video.addEventListener('error', (e) => {
        console.log('Video error:', e);
        setVideoStatus('error');
      });
      
      // Ensure video plays
      video.play().catch((error) => {
        console.log('Video autoplay failed:', error);
      });
    } else {
      console.log('Video element not found');
    }
  }, []);

  return (
    <>
      {/* Hero Section Start */}
      <div 
        className="hero hero-bg-image hero-video"
        style={{
          position: 'relative',
          overflow: 'hidden',
          backgroundImage: 'url(/images/hero-bg.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        {/* Video Start */}
        <div className="hero-bg-video">
          {/* Selfhosted Video Start */}
          {/* <video autoplay muted loop id="myvideo"><source src="/images/hero-bg-video.mp4" type="video/mp4"></video> */}
          <video 
            ref={videoRef}
            autoPlay 
            muted 
            loop 
            playsInline
            id="myvideo"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              zIndex: -1
            }}
          >
            <source
              src="/videos/Hero-video.mp4"
              type="video/mp4"
            />
            
            Your browser does not support the video tag.
          </video>
          {/* Selfhosted Video End */}
          {/* Youtube Video Start */}
          {/* <div id="herovideo" class="player" data-property="{videoURL:'OjTRVpgtcG4',containment:'.hero-video', showControls:false, autoPlay:true, loop:true, vol:0, mute:false, startAt:0,  stopAt:296, opacity:1, addRaster:true, quality:'large', optimizeDisplay:true}"></div> */}
          {/* Youtube Video End */}
        </div>
        {/* Video End */}
        
        {/* Debug Info - Remove this in production */}
        <div style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          background: 'rgba(0,0,0,0.7)',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '5px',
          fontSize: '12px',
          zIndex: 1000
        }}>
          Video Status: {videoStatus}
        </div>
        
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-12">
              {/* Hero Content Start */}
              <div className="hero-content">
                {/* Section Title Start */}
                <div className="section-title section-title-center">
                  <h3 className="wow fadeInUp">
                    Protect your business from cyber threat
                  </h3>
                  <h1
                    className="wow fadeInUp"
                    data-wow-delay="0.2s"
                    data-cursor="-opaque"
                  >
                    Advanced cybersecurity solutions tailored to{" "}
                    <span>keep you safe</span>
                  </h1>
                  <p className="wow fadeInUp" data-wow-delay="0.4s">
                    In a world where cyberattacks are becoming more sophisticated,
                    your business deserves the best protection. Our expert team
                    leverages cutting-edge technology.
                  </p>
                </div>
                {/* Section Title End */}
                {/* Hero Button Start */}
                <div className="hero-btn wow fadeInUp" data-wow-delay="0.6s">
                  <a href="contact.html" className="btn-default btn-highlighted">
                    Get Free Assessment
                  </a>
                  <a href="services.html" className="btn-default">
                    View our services
                  </a>
                </div>
                {/* Hero Button End */}
              </div>
              {/* Hero Content End */}
            </div>
          </div>
          <div className="row">
            <div className="col-lg-12">
              {/* Hero Company Slider Start */}
              <div className="hero-company-slider">
                <p>
                  We're Trusted by more than <span className="counter">1000</span>+
                  companies
                </p>
                <div className="swiper">
                  <div className="swiper-wrapper">
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-1.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-2.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-3.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-4.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-1.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-2.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                    {/* Company Logo Start */}
                    <div className="swiper-slide">
                      <div className="company-logo">
                        <img src="/images/company-logo-3.svg" alt="" />
                      </div>
                    </div>
                    {/* Company Logo End */}
                  </div>
                </div>
              </div>
              {/* Hero Company Slider End */}
            </div>
          </div>
        </div>
      </div>
      {/* Hero Section End */}
    </>
  );
};

export default HomeVideoHero;
