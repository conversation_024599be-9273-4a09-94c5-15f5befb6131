'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface MenuItem {
  label: string;
  href: string;
  icon?: string;
}

interface SubmenuItem {
  label: string;
  href: string;
  description?: string;
  icon?: string;
}

interface MenuWithSubmenu {
  label: string;
  items: SubmenuItem[];
}

const navigationData = {
  home: {
    label: 'Home',
    items: [
      { label: 'Home - Main', href: '/', description: 'Default homepage layout' },
      { label: 'Home - Image', href: '/', description: 'Hero with background image' },
      { label: 'Home - Video', href: '/', description: 'Hero with background video' },
      { label: 'Home - Slider', href: '/', description: 'Hero with image slider' },
    ]
  },
  pages: {
    label: 'Pages',
    items: [
      { label: 'Service Details', href: '/services', description: 'Individual service page' },
      { label: 'Blog Details', href: '/blog', description: 'Blog post layout' },
      { label: 'Case Study', href: '/portfolio', description: 'Portfolio showcase' },
      { label: 'Case Study details', href: '/portfolio', description: 'Detailed case study' },
      { label: 'Our Team', href: '/about', description: 'Team members overview' },
      { label: 'Team Details', href: '/about', description: 'Individual team member' },
      { label: 'Testimonials', href: '/about', description: 'Client testimonials' },
      { label: 'Image Gallery', href: '/portfolio', description: 'Photo gallery' },
      { label: 'Video Gallery', href: '/portfolio', description: 'Video showcase' },
      { label: 'FAQs', href: '/contact', description: 'Frequently asked questions' },
      { label: '404', href: '/not-found', description: 'Error page' },
    ]
  }
};

const mainMenuItems: MenuItem[] = [
  { label: 'About Us', href: '/about' },
  { label: 'Services', href: '/services' },
  { label: 'Blog', href: '/blog' },
  { label: 'Pricing', href: '/contact' },
];

const ModernSubmenu: React.FC<{
  data: MenuWithSubmenu;
  isOpen: boolean;
  onToggle: () => void;
}> = ({ data, isOpen, onToggle }) => (
  <li className="relative group">
    <button
      className="flex items-center gap-1 text-gray-700 hover:text-blue-600"
      aria-haspopup="true"
      aria-expanded={isOpen}
      onClick={onToggle}
    >
      {data.label}
      <svg
        className={`w-3 h-3 transition-transform ${isOpen ? 'rotate-180' : ''}`}
        viewBox="0 0 12 12"
      >
        <path d="M2 4L6 8L10 4" stroke="currentColor" strokeWidth="2" fill="none" />
      </svg>
    </button>
    {isOpen && (
      <div className="absolute top-full left-0 mt-2 w-56 bg-white shadow-lg border rounded">
        {data.items.map((item, index) => (
          <Link key={index} href={item.href} className="block px-4 py-2 hover:bg-gray-100">
            <div className="font-medium">{item.label}</div>
            {item.description && (
              <div className="text-sm text-gray-500">{item.description}</div>
            )}
          </Link>
        ))}
      </div>
    )}
  </li>
);

const ModernMenuItem: React.FC<{ item: MenuItem }> = ({ item }) => (
  <li>
    <Link href={item.href} className="text-gray-700 hover:text-blue-600">
      {item.label}
    </Link>
  </li>
);

const ModernLogo: React.FC = () => (
  <Link href="/" className="flex-shrink-0">
    <Image
      src="/images/logo.svg"
      alt="Technoloway"
      width={140}
      height={50}
      priority
    />
  </Link>
);

const ModernContactButton: React.FC = () => (
  <Link
    href="/contact"
    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
  >
    Get Started
  </Link>
);

const MobileMenu: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  submenuOpen: { [key: string]: boolean };
  toggleSubmenu: (key: string) => void;
}> = ({ isOpen, onClose, submenuOpen, toggleSubmenu }) => (
  <div
    className={`fixed inset-0 bg-white z-50 p-6 transform transition-transform ${
      isOpen ? 'translate-x-0' : '-translate-x-full'
    }`}
  >
    <div className="flex justify-between items-center mb-6">
      <ModernLogo />
      <button onClick={onClose} className="text-gray-600">
        ✕
      </button>
    </div>
    <ul className="space-y-4">
      <li>
        <button
          className="flex justify-between items-center w-full text-left"
          onClick={() => toggleSubmenu('home')}
        >
          Home
          <span>{submenuOpen.home ? '▲' : '▼'}</span>
        </button>
        {submenuOpen.home && (
          <ul className="pl-4 mt-2 space-y-2">
            {navigationData.home.items.map((item, index) => (
              <li key={index}>
                <Link href={item.href} className="block">
                  {item.label}
                </Link>
              </li>
            ))}
          </ul>
        )}
      </li>

      {mainMenuItems.map((item, index) => (
        <li key={index}>
          <Link href={item.href} className="block">
            {item.label}
          </Link>
        </li>
      ))}

      <li>
        <button
          className="flex justify-between items-center w-full text-left"
          onClick={() => toggleSubmenu('pages')}
        >
          Pages
          <span>{submenuOpen.pages ? '▲' : '▼'}</span>
        </button>
        {submenuOpen.pages && (
          <ul className="pl-4 mt-2 space-y-2">
            {navigationData.pages.items.map((item, index) => (
              <li key={index}>
                <Link href={item.href} className="block">
                  {item.label}
                </Link>
              </li>
            ))}
          </ul>
        )}
      </li>
    </ul>

    <div className="mt-6">
      <ModernContactButton />
    </div>
  </div>
);

export default function Navbar() {
  const [menuOpen, setMenuOpen] = useState(false);
  const [submenuOpen, setSubmenuOpen] = useState<{ [key: string]: boolean }>({});
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => setIsScrolled(window.scrollY > 20);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleSubmenu = (key: string) => {
    setSubmenuOpen((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  return (
    <>
      <header className={`fixed top-0 left-0 w-full bg-white shadow-sm z-50 ${isScrolled ? 'py-2' : 'py-4'}`}>
        <div className="container mx-auto flex items-center justify-between">
          <ModernLogo />

          {/* Desktop Menu */}
          <nav className="hidden lg:flex flex-1 justify-center">
            <ul className="flex items-center gap-8">
              <ModernSubmenu
                data={navigationData.home}
                isOpen={submenuOpen.home || false}
                onToggle={() => toggleSubmenu('home')}
              />
              {mainMenuItems.map((item, index) => (
                <ModernMenuItem key={index} item={item} />
              ))}
              <ModernSubmenu
                data={navigationData.pages}
                isOpen={submenuOpen.pages || false}
                onToggle={() => toggleSubmenu('pages')}
              />
            </ul>
          </nav>

          {/* Contact Button */}
          <div className="hidden lg:block">
            <ModernContactButton />
          </div>

          {/* Mobile Toggle */}
          <button
            className="lg:hidden p-2"
            onClick={() => setMenuOpen(true)}
            aria-label="Open Menu"
          >
            ☰
          </button>
        </div>
      </header>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={menuOpen}
        onClose={() => setMenuOpen(false)}
        submenuOpen={submenuOpen}
        toggleSubmenu={toggleSubmenu}
      />
    </>
  );
}
