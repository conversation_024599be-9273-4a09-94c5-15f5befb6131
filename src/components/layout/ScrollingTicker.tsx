'use client';

import React from 'react';
import Image from 'next/image';

const industries = [
  'Healthcare',
  'Finance and Banking',
  'Legal and Law Firms',
  'Government and Public Sector',
  'Technology and Software'
];

export default function ScrollingTicker() {
  return (
    <>
      {/* Scrolling Ticker Section Start */}
      <div className="our-scrolling-ticker">
        {/* Scrolling Ticker Start */}
        <div className="scrolling-ticker-box">
          <div className="scrolling-content">
            {industries.map((industry, index) => (
              <span key={index}>
                <Image 
                  src="/images/star-icon.svg" 
                  alt="Star" 
                  width={20} 
                  height={20} 
                  style={{ display: 'inline-block', verticalAlign: 'middle', marginRight: '8px' }} 
                />
                {industry}
              </span>
            ))}
            {/* Duplicate for seamless loop */}
            {industries.map((industry, index) => (
              <span key={`duplicate-${index}`}>
                <Image 
                  src="/images/star-icon.svg" 
                  alt="Star" 
                  width={20} 
                  height={20} 
                  style={{ display: 'inline-block', verticalAlign: 'middle', marginRight: '8px' }} 
                />
                {industry}
              </span>
            ))}
          </div>
          <div className="scrolling-content">
            {industries.map((industry, index) => (
              <span key={`second-${index}`}>
                <Image 
                  src="/images/star-icon.svg" 
                  alt="Star" 
                  width={20} 
                  height={20} 
                  style={{ display: 'inline-block', verticalAlign: 'middle', marginRight: '8px' }} 
                />
                {industry}
              </span>
            ))}
            {/* Duplicate for seamless loop */}
            {industries.map((industry, index) => (
              <span key={`second-duplicate-${index}`}>
                <Image 
                  src="/images/star-icon.svg" 
                  alt="Star" 
                  width={20} 
                  height={20} 
                  style={{ display: 'inline-block', verticalAlign: 'middle', marginRight: '8px' }} 
                />
                {industry}
              </span>
            ))}
          </div>
        </div>
        {/* Scrolling Ticker End */}
      </div>
      {/* Scrolling Ticker Section End */}
    </>
  );
}
