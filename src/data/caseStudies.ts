export interface CaseStudy {
  id: string;
  slug: string;
  title: string;
  image: string;
  category: string;
  client: string;
  date: string;
  duration: string;
  projectName: string;
  overview: string;
  challenges: string;
  solutions: string[];
  gallery: string[];
  faqs: Array<{
    question: string;
    answer: string;
  }>;
}

export const caseStudies: CaseStudy[] = [
  {
    id: '1',
    slug: 'strengthening-cybersecurity-law-firm',
    title: 'Strengthening Cybersecurity for a Law Firm',
    image: '/images/case-study-image-1.jpg',
    category: 'Data protection for law firms',
    client: '<PERSON><PERSON>',
    date: '18 January, 2025',
    duration: '3 months',
    projectName: 'Cybersecurity consulting',
    overview: 'By keeping an eye on these trends & integrating them into your cybersecurity strategies, you can stay one step ahead of cybercriminals and better protect your business in an increasingly interconnected world. Cybersecurity is constantly evolving. Explore the key trends and technologies set to shape the security landscape in 2025 and beyond. Whether you\'re a business owner or an IT professional, understanding these trends is crucial for maintaining robust defenses against ever-growing cyber threats. Stay ahead of the curve with these emerging cybersecurity trends for 2025. From AI-driven defense systems to blockchain security, here\'s what you need to watch.',
    challenges: 'By keeping an eye on these trends and integrating them into your cybersecurity strategies, you can stay one step ahead of cybercriminals and better protect your business in an increasingly interconnected world. Cybersecurity is constantly evolving.',
    solutions: [
      'Proactively identify and neutralize potential threats with AI-powered tools',
      'Safeguard your sensitive data with state-of-the-art encryption and secure protocols',
      'Minimize downtime and disruptions by implementing strong disaster recovery plans'
    ],
    gallery: [
      '/images/case-study-image-1.jpg',
      '/images/case-study-image-2.jpg',
      '/images/case-study-image-3.jpg'
    ],
    faqs: [
      {
        question: 'What types of cybersecurity services do you offer?',
        answer: 'We offer a comprehensive range of cybersecurity services including threat detection, incident response, security audits, penetration testing, and compliance consulting.'
      },
      {
        question: 'How long does a typical cybersecurity assessment take?',
        answer: 'A comprehensive cybersecurity assessment typically takes 2-4 weeks depending on the size and complexity of your organization.'
      },
      {
        question: 'Do you provide ongoing security monitoring?',
        answer: 'Yes, we offer 24/7 security monitoring services to ensure your systems are protected around the clock.'
      },
      {
        question: 'What compliance standards do you help with?',
        answer: 'We help organizations comply with various standards including GDPR, HIPAA, SOC 2, ISO 27001, and industry-specific regulations.'
      },
      {
        question: 'How do you handle data breaches?',
        answer: 'We have a comprehensive incident response plan that includes immediate containment, investigation, remediation, and communication protocols.'
      }
    ]
  },
  {
    id: '2',
    slug: 'cloud-infrastructure-saas-company',
    title: 'Cloud Infrastructure for a SaaS Company',
    image: '/images/case-study-image-2.jpg',
    category: 'Cloud security implementation',
    client: 'Michael Chen',
    date: '15 February, 2025',
    duration: '4 months',
    projectName: 'Cloud security consulting',
    overview: 'Implementing robust cloud security measures for a growing SaaS company to protect customer data and ensure compliance with industry standards. The project involved securing multi-cloud environments and implementing zero-trust architecture.',
    challenges: 'The client needed to secure their rapidly expanding cloud infrastructure while maintaining high availability and performance for their SaaS platform.',
    solutions: [
      'Implemented zero-trust security architecture across all cloud environments',
      'Deployed advanced threat detection and monitoring systems',
      'Established comprehensive backup and disaster recovery procedures'
    ],
    gallery: [
      '/images/case-study-image-2.jpg',
      '/images/case-study-image-3.jpg',
      '/images/case-study-image-4.jpg'
    ],
    faqs: [
      {
        question: 'What cloud platforms do you secure?',
        answer: 'We secure all major cloud platforms including AWS, Azure, Google Cloud, and hybrid cloud environments.'
      },
      {
        question: 'How do you ensure cloud security compliance?',
        answer: 'We implement industry-standard security frameworks and conduct regular compliance audits to ensure your cloud environment meets all requirements.'
      },
      {
        question: 'What is zero-trust architecture?',
        answer: 'Zero-trust architecture is a security model that requires verification for every user and device attempting to access resources, regardless of their location.'
      }
    ]
  },
  {
    id: '3',
    slug: 'financial-data-global-investment-firm',
    title: 'Financial Data for a Global Investment Firm',
    image: '/images/case-study-image-3.jpg',
    category: 'Financial services security',
    client: 'Sarah Johnson',
    date: '10 March, 2025',
    duration: '6 months',
    projectName: 'Financial cybersecurity',
    overview: 'Securing sensitive financial data and trading systems for a global investment firm. The project focused on protecting against sophisticated cyber threats while ensuring regulatory compliance.',
    challenges: 'The firm needed to protect highly sensitive financial data while maintaining real-time trading capabilities and meeting strict regulatory requirements.',
    solutions: [
      'Implemented advanced encryption for all financial transactions',
      'Deployed real-time threat monitoring and response systems',
      'Established comprehensive audit trails for regulatory compliance'
    ],
    gallery: [
      '/images/case-study-image-3.jpg',
      '/images/case-study-image-4.jpg',
      '/images/case-study-image-5.jpg'
    ],
    faqs: [
      {
        question: 'How do you protect financial data?',
        answer: 'We use advanced encryption, multi-factor authentication, and real-time monitoring to protect sensitive financial information.'
      },
      {
        question: 'What regulatory standards do you help with?',
        answer: 'We help financial institutions comply with SOX, PCI DSS, GLBA, and other relevant financial regulations.'
      }
    ]
  },
  {
    id: '4',
    slug: 'secure-remote-work-environment',
    title: 'Building a Secure Remote Work Environment',
    image: '/images/case-study-image-4.jpg',
    category: 'Remote work security',
    client: 'David Rodriguez',
    date: '5 April, 2025',
    duration: '2 months',
    projectName: 'Remote work security',
    overview: 'Creating a secure remote work environment for a technology company with distributed teams. The project focused on securing endpoints, implementing VPN solutions, and establishing security policies.',
    challenges: 'The company needed to quickly transition to remote work while maintaining security standards and protecting corporate data.',
    solutions: [
      'Implemented secure VPN and remote access solutions',
      'Deployed endpoint security and device management tools',
      'Established comprehensive remote work security policies'
    ],
    gallery: [
      '/images/case-study-image-4.jpg',
      '/images/case-study-image-5.jpg',
      '/images/case-study-image-6.jpg'
    ],
    faqs: [
      {
        question: 'How do you secure remote workers?',
        answer: 'We implement VPN solutions, endpoint security, and comprehensive policies to ensure remote workers can access resources securely.'
      },
      {
        question: 'What devices do you support?',
        answer: 'We support all major operating systems and device types including Windows, macOS, Linux, and mobile devices.'
      }
    ]
  },
  {
    id: '5',
    slug: 'advanced-threat-detection',
    title: 'Developing Advanced Threat Detection',
    image: '/images/case-study-image-5.jpg',
    category: 'Threat detection and response',
    client: 'Lisa Thompson',
    date: '20 May, 2025',
    duration: '5 months',
    projectName: 'Advanced threat detection',
    overview: 'Developing and implementing advanced threat detection systems for a healthcare organization. The project focused on protecting patient data and ensuring HIPAA compliance.',
    challenges: 'The healthcare organization needed to detect and respond to sophisticated cyber threats while maintaining patient privacy and HIPAA compliance.',
    solutions: [
      'Implemented AI-powered threat detection systems',
      'Deployed behavioral analytics and anomaly detection',
      'Established automated incident response procedures'
    ],
    gallery: [
      '/images/case-study-image-5.jpg',
      '/images/case-study-image-6.jpg',
      '/images/case-study-image-1.jpg'
    ],
    faqs: [
      {
        question: 'How does AI threat detection work?',
        answer: 'AI threat detection uses machine learning algorithms to analyze network traffic and identify patterns that indicate potential security threats.'
      },
      {
        question: 'What is behavioral analytics?',
        answer: 'Behavioral analytics monitors user and system behavior to identify unusual patterns that may indicate security threats.'
      }
    ]
  },
  {
    id: '6',
    slug: 'cybersecurity-education-institution',
    title: 'Cybersecurity Implementation for a Leading Education Institution',
    image: '/images/case-study-image-6.jpg',
    category: 'Education sector security',
    client: 'Robert Wilson',
    date: '12 June, 2025',
    duration: '4 months',
    projectName: 'Education cybersecurity',
    overview: 'Implementing comprehensive cybersecurity measures for a leading education institution. The project focused on protecting student data and securing online learning platforms.',
    challenges: 'The institution needed to secure their online learning platforms and protect sensitive student information while maintaining accessibility for students and faculty.',
    solutions: [
      'Secured online learning platforms and student portals',
      'Implemented data protection measures for student records',
      'Established cybersecurity training programs for faculty and staff'
    ],
    gallery: [
      '/images/case-study-image-6.jpg',
      '/images/case-study-image-1.jpg',
      '/images/case-study-image-2.jpg'
    ],
    faqs: [
      {
        question: 'How do you protect student data?',
        answer: 'We implement strict access controls, encryption, and regular security audits to protect sensitive student information.'
      },
      {
        question: 'Do you provide cybersecurity training?',
        answer: 'Yes, we provide comprehensive cybersecurity training programs for faculty, staff, and students.'
      }
    ]
  }
];

export function getCaseStudyBySlug(slug: string): CaseStudy | undefined {
  return caseStudies.find(caseStudy => caseStudy.slug === slug);
}

export function getAllCaseStudies(): CaseStudy[] {
  return caseStudies;
}
