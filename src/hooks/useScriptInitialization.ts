import { useEffect, useState } from 'react';

export const useScriptInitialization = () => {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    let mounted = true;

    const initializeScripts = () => {
      if (!mounted) return;

      // Hide preloader with fade-out effect
      const preloader = document.querySelector('.preloader') as HTMLElement;
      if (preloader) {
        preloader.classList.add('hidden');
        setTimeout(() => {
          preloader.style.display = 'none';
        }, 500);
      }

      // Set scripts loaded flag
      if (typeof window !== 'undefined') {
        window.scriptsLoaded = true;
      }
      
      // Initialize scripts when they're available
      const initScripts = () => {
        if (typeof window !== 'undefined' && 
            window.jQuery && 
            window.jQuery.fn) {
          
          // Remove any existing SlickNav elements
          const existingSlicknav = document.querySelector('.slicknav_menu');
          if (existingSlicknav) {
            existingSlicknav.remove();
          }
          
          // Initialize SlickNav only if it's available
          if (window.jQuery.fn.slicknav) {
            window.jQuery('#menu').slicknav({
              prependTo: '.responsive-menu',
              label: '',
              allowParentLinks: true,
              closedSymbol: '<i class="fa-solid fa-plus"></i>',
              openedSymbol: '<i class="fa-solid fa-minus"></i>'
            });
          }

          // Magic cursor is handled automatically by the script
          if ((window as any).gsap && window.jQuery) {
            console.log('Magic cursor should be working automatically');
          }

          setIsInitialized(true);
        }
      };

      // Try to initialize immediately
      initScripts();

      // If not ready, wait for scripts to load
      if (typeof window !== 'undefined' && !window.jQuery) {
        const checkScripts = setInterval(() => {
          if (typeof window !== 'undefined' && window.jQuery) {
            clearInterval(checkScripts);
            initScripts();
          }
        }, 100);

        // Timeout after 10 seconds to prevent infinite waiting
        setTimeout(() => {
          clearInterval(checkScripts);
          setIsInitialized(true); // Set to true anyway to not block rendering
        }, 10000);
      } else {
        setIsInitialized(true);
      }
    };

    // Initialize immediately
    initializeScripts();

    return () => {
      mounted = false;
    };
  }, []);

  return isInitialized;
};
