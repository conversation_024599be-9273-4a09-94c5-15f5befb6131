interface EmailData {
  to: string;
  subject: string;
  text: string;
  html?: string;
  from?: string;
}

// Example implementation with SendGrid
export async function sendEmailWithSendGrid(data: EmailData) {
  const SENDGRID_API_KEY = process.env.SENDGRID_API_KEY;
  
  if (!SENDGRID_API_KEY) {
    throw new Error('SendGrid API key not configured');
  }

  const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${SENDGRID_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      personalizations: [
        {
          to: [{ email: data.to }],
        },
      ],
      from: { email: data.from || '<EMAIL>' },
      subject: data.subject,
      content: [
        {
          type: 'text/plain',
          value: data.text,
        },
        ...(data.html ? [{
          type: 'text/html',
          value: data.html,
        }] : []),
      ],
    }),
  });

  if (!response.ok) {
    throw new Error(`SendGrid API error: ${response.statusText}`);
  }

  return response.json();
}

// Example implementation with Mailgun
export async function sendEmailWithMailgun(data: EmailData) {
  const MAILGUN_API_KEY = process.env.MAILGUN_API_KEY;
  const MAILGUN_DOMAIN = process.env.MAILGUN_DOMAIN;
  
  if (!MAILGUN_API_KEY || !MAILGUN_DOMAIN) {
    throw new Error('Mailgun configuration not found');
  }

  const formData = new FormData();
  formData.append('from', data.from || `noreply@${MAILGUN_DOMAIN}`);
  formData.append('to', data.to);
  formData.append('subject', data.subject);
  formData.append('text', data.text);
  
  if (data.html) {
    formData.append('html', data.html);
  }

  const response = await fetch(`https://api.mailgun.net/v3/${MAILGUN_DOMAIN}/messages`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${btoa(`api:${MAILGUN_API_KEY}`)}`,
    },
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`Mailgun API error: ${response.statusText}`);
  }

  return response.json();
}

// Example implementation with Nodemailer (for SMTP)
export async function sendEmailWithNodemailer(data: EmailData) {
  // This would require installing nodemailer: npm install nodemailer
  // import nodemailer from 'nodemailer';
  
  // const transporter = nodemailer.createTransporter({
  //   host: process.env.SMTP_HOST,
  //   port: parseInt(process.env.SMTP_PORT || '587'),
  //   secure: false,
  //   auth: {
  //     user: process.env.SMTP_USER,
  //     pass: process.env.SMTP_PASS,
  //   },
  // });
  
  // return transporter.sendMail({
  //   from: data.from || process.env.SMTP_USER,
  //   to: data.to,
  //   subject: data.subject,
  //   text: data.text,
  //   html: data.html,
  // });
  
  throw new Error('Nodemailer implementation requires nodemailer package');
}

// Main email function - choose your preferred service
export async function sendEmail(data: EmailData) {
  // Choose your email service here
  const emailService = process.env.EMAIL_SERVICE || 'sendgrid';
  
  switch (emailService.toLowerCase()) {
    case 'sendgrid':
      return sendEmailWithSendGrid(data);
    case 'mailgun':
      return sendEmailWithMailgun(data);
    case 'nodemailer':
      return sendEmailWithNodemailer(data);
    default:
      throw new Error(`Unsupported email service: ${emailService}`);
  }
}
