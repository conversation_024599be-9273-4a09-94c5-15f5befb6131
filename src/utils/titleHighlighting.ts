// Smart title highlighting utility
export interface HighlightedTitle {
  title: string;
  titleHighlight: string;
}

// Words that are typically not important enough to highlight
const UNIMPORTANT_WORDS = new Set([
  'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
  'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
  'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall',
  'this', 'that', 'these', 'those', 'it', 'its', 'they', 'them', 'their',
  'from', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
  'up', 'down', 'out', 'off', 'over', 'under', 'again', 'further', 'then', 'once',
  'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such',
  'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 's', 't', 'can', 'will', 'just', 'don', 'should', 'now'
]);

// Words that are typically important and should be highlighted
const IMPORTANT_WORDS = new Set([
  'cybersecurity', 'security', 'cyber', 'hacking', 'threat', 'attack', 'breach', 'vulnerability',
  'encryption', 'firewall', 'malware', 'phishing', 'ransomware', 'virus', 'trojan', 'spyware',
  'authentication', 'authorization', 'compliance', 'audit', 'monitoring', 'detection', 'response',
  'consulting', 'services', 'solutions', 'protection', 'defense', 'prevention', 'mitigation',
  'training', 'awareness', 'education', 'certification', 'expertise', 'experience', 'professional',
  'enterprise', 'business', 'corporate', 'organization', 'company', 'industry', 'sector',
  'technology', 'digital', 'online', 'internet', 'web', 'cloud', 'network', 'system',
  'data', 'information', 'privacy', 'confidential', 'sensitive', 'personal', 'financial',
  'risk', 'assessment', 'analysis', 'strategy', 'planning', 'implementation', 'deployment',
  'management', 'administration', 'operation', 'maintenance', 'support', 'consultation',
  'development', 'design', 'architecture', 'infrastructure', 'platform', 'application',
  'mobile', 'desktop', 'server', 'database', 'backup', 'recovery', 'disaster', 'incident',
  'forensic', 'investigation', 'evidence', 'compliance', 'regulation', 'policy', 'procedure',
  'framework', 'standard', 'protocol', 'algorithm', 'encryption', 'decryption', 'key',
  'password', 'credential', 'identity', 'access', 'control', 'permission', 'privilege',
  'session', 'token', 'certificate', 'signature', 'hash', 'checksum', 'integrity',
  'availability', 'confidentiality', 'authenticity', 'non-repudiation', 'accountability'
]);

// Words that should never be highlighted (common endings, prepositions, etc.)
const NEVER_HIGHLIGHT = new Set([
  'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'into', 'through', 'during',
  'before', 'after', 'above', 'below', 'up', 'down', 'out', 'off', 'over', 'under',
  'the', 'a', 'an', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
  'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may',
  'might', 'can', 'must', 'shall', 'this', 'that', 'these', 'those', 'it', 'its', 'they',
  'them', 'their', 'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both',
  'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only',
  'own', 'same', 'so', 'than', 'too', 'very', 'just', 'now', 'then', 'once', 'again',
  'further', 's', 't', 'can', 'will', 'don', 'should', 'now', 'year', 'years', '2024', '2025'
]);

/**
 * Calculates the importance score of a word based on various factors
 */
function calculateWordImportance(word: string, position: number, totalWords: number): number {
  const lowerWord = word.toLowerCase();
  
  // Never highlight certain words
  if (NEVER_HIGHLIGHT.has(lowerWord)) {
    return 0;
  }
  
  let score = 0;
  
  // Bonus for important cybersecurity/business words
  if (IMPORTANT_WORDS.has(lowerWord)) {
    score += 10;
  }
  
  // Penalty for unimportant words
  if (UNIMPORTANT_WORDS.has(lowerWord)) {
    score -= 5;
  }
  
  // Bonus for longer words (more specific/technical)
  if (word.length > 6) {
    score += 3;
  }
  
  // Bonus for capitalized words (proper nouns, brand names)
  if (word[0] === word[0]?.toUpperCase() && word.length > 2) {
    score += 2;
  }
  
  // Bonus for words with numbers (years, versions)
  if (/\d/.test(word)) {
    score += 4;
  }
  
  // Bonus for technical terms (words with special characters or patterns)
  if (/[A-Z]{2,}/.test(word) || /[&@#$%]/.test(word)) {
    score += 3;
  }
  
  // Position bonus (words at the end are often more important)
  const positionScore = (position / totalWords) * 2;
  score += positionScore;
  
  return score;
}

/**
 * Smart function to split a title and highlight the most important words
 */
export function smartHighlightTitle(fullTitle: string): HighlightedTitle {
  const words = fullTitle.split(' ');
  
  if (words.length <= 1) {
    return {
      title: fullTitle,
      titleHighlight: ''
    };
  }
  
  if (words.length === 2) {
    // For 2-word titles, highlight the second word if it's important
    const secondWordScore = calculateWordImportance(words[1], 1, 2);
    if (secondWordScore > 3) {
      return {
        title: words[0],
        titleHighlight: words[1]
      };
    }
    return {
      title: fullTitle,
      titleHighlight: ''
    };
  }
  
  // Calculate importance scores for all words
  const wordScores = words.map((word, index) => ({
    word,
    score: calculateWordImportance(word, index, words.length),
    index
  }));
  
  // Sort by score (highest first)
  wordScores.sort((a, b) => b.score - a.score);
  
  // Find the best word to highlight
  let bestWord = wordScores[0];
  
  // If the best word has a very low score, don't highlight anything
  if (bestWord.score < 2) {
    return {
      title: fullTitle,
      titleHighlight: ''
    };
  }
  
  // Create the split
  const highlightIndex = bestWord.index;
  const titleWords = words.slice(0, highlightIndex);
  const highlightWords = words.slice(highlightIndex);
  
  return {
    title: titleWords.join(' '),
    titleHighlight: highlightWords.join(' ')
  };
}

/**
 * Alternative function for specific contexts (e.g., team member names)
 */
export function highlightTeamMemberName(fullName: string): HighlightedTitle {
  const nameParts = fullName.split(' ');
  
  if (nameParts.length <= 1) {
    return {
      title: fullName,
      titleHighlight: ''
    };
  }
  
  // For names, always highlight the last name
  const firstName = nameParts.slice(0, -1).join(' ');
  const lastName = nameParts.slice(-1)[0];
  
  return {
    title: firstName,
    titleHighlight: lastName
  };
}

/**
 * Function for service titles - highlight the service type
 */
export function highlightServiceTitle(serviceTitle: string): HighlightedTitle {
  const words = serviceTitle.split(' ');
  
  // Look for service-related keywords to highlight
  const serviceKeywords = ['services', 'solutions', 'consulting', 'management', 'protection', 'security', 'monitoring', 'training'];
  
  for (let i = words.length - 1; i >= 0; i--) {
    const word = words[i].toLowerCase();
    if (serviceKeywords.includes(word)) {
      return {
        title: words.slice(0, i).join(' '),
        titleHighlight: words.slice(i).join(' ')
      };
    }
  }
  
  // Fallback to smart highlighting
  return smartHighlightTitle(serviceTitle);
}

/**
 * Function for blog post titles - highlight key topics
 */
export function highlightBlogTitle(blogTitle: string): HighlightedTitle {
  const words = blogTitle.split(' ');
  
  // Look for action words or key topics to highlight
  const actionWords = ['guide', 'tips', 'strategies', 'best', 'practices', 'trends', 'analysis', 'report', 'study'];
  
  for (let i = words.length - 1; i >= 0; i--) {
    const word = words[i].toLowerCase();
    if (actionWords.includes(word)) {
      return {
        title: words.slice(0, i).join(' '),
        titleHighlight: words.slice(i).join(' ')
      };
    }
  }
  
  // Fallback to smart highlighting
  return smartHighlightTitle(blogTitle);
}
