<!doctype html>  
<!--[if IE 6 ]><html lang="en-us" class="ie6"> <![endif]-->
<!--[if IE 7 ]><html lang="en-us" class="ie7"> <![endif]-->
<!--[if IE 8 ]><html lang="en-us" class="ie8"> <![endif]-->
<!--[if (gt IE 7)|!(IE)]><!-->
<html lang="en-us"><!--<![endif]-->
<head>
	<meta charset="utf-8">
	
	<title>Documentation Firevall - Cyber Security Services HTML Template</title>
	
	<meta name="description" content="
		Created by: Awaiken
		Support: &#115;&#117;&#112;&#112;&#111;&#114;&#116;&#64;&#97;&#119;&#97;&#105;&#107;&#101;&#110;&#46;&#99;&#111;&#109;
	">
	<meta name="author" content="Awaiken">
	<meta name="copyright" content="Awaiken">
	<meta name="generator" content="Documenter v2.0 http://rxa.li/documenter">
	<META NAME="robots" CONTENT="noindex,nofollow">
	<link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,500,600,700,800,900" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css?family=Poppins:400,500,600,700" rel="stylesheet">

	<link rel="stylesheet" href="assets/css/documenter_style.css" media="all">
	<link rel="stylesheet" href="assets/js/google-code-prettify/prettify.css" media="screen">
	<script src="assets/js/google-code-prettify/prettify.js"></script>

	
	
	<script src="assets/js/jquery.js"></script>
	
	<script src="assets/js/jquery.scrollTo.js"></script>
	<script src="assets/js/jquery.easing.js"></script>
	
	<script>document.createElement('section');var duration='500',easing='swing';</script>
	<script src="assets/js/script.js"></script>
	
	<style>
		html{background-color:#FAFAFA;color:#333;font-family: 'Poppins', sans-serif;
}
		::-moz-selection{background:#000;color:#fff;}
		::selection{background:#000;color:#fff;}
		#documenter_sidebar #documenter_logo{background-image:url(assets/images/image_1.png);}
		a{color:#000000;}
		.btn {
			border-radius:3px;
		}
		hr{border-top:1px solid #e2e2e2;border-bottom:1px solid #fff;}
		#documenter_sidebar, #documenter_sidebar ul a{background-color:#1C1B2B;color:#fff;}
		#documenter_sidebar ul a{-webkit-text-shadow:1px 1px 0px #000;-moz-text-shadow:1px 1px 0px #444;text-shadow:1px 1px 0px #6B5344;}
		#documenter_sidebar ul{border-top:1px solid #30251F;}
		#documenter_sidebar ul a{border-top:1px solid #ffffff20;color:#F7F7F7;}
		#documenter_sidebar ul a:hover{background:#ffffff10;color:#fff;border-top:1px solid #ffffff20;}
		#documenter_sidebar ul a.current{background:#B42FDA;color:#FFFFFF;border-top:1px solid #ffffff20;}
		#documenter_copyright{display:block !important;visibility:visible !important;}
	</style>
	
</head>
<body class="documenter-project-folder">
	<div id="documenter_sidebar">
		<a href="#documenter_cover" id="documenter_logo"></a>
		<ul id="documenter_nav">
			<li><a class="current" href="#documenter_cover">Start</a></li>
			<li><a href="#introdution" title="Introdution">Introdution</a></li>
			<li><a href="#folders_files" title="Folders & Files">Folders & Files</a></li>
			<li><a href="#css_files" title="CSS Files">CSS Files</a></li>
			<li><a href="#javascript" title="JavaScript">JavaScript</a></li>
			<li><a href="#video" title="PHP">Hero video background</a></li>
			<li><a href="#php" title="PHP">Contact form PHP</a></li>
			<li><a href="#php" title="PHP">Appointment form PHP</a></li>
			<li><a href="#icons" title="Youtube Video">Icons</a></li>
			<li><a href="#sources_and_credits" title="Sources and Credits">Sources and Credits</a></li>
		</ul>
		<div id="documenter_copyright">Copyright Awaiken 2024<br>
		made with the <a href="http://rxa.li/documenter">Documenter v2.0</a> 
		</div>
	</div>
	<div id="documenter_content">
	<section id="documenter_cover"> 
	<h1>Firevall</h1>
	<h2>Cyber Security Services HTML Template</h2>
	<div id="documenter_buttons">
		
	</div>
	<hr>
	<ul>
	
	<li>Live Preview : <a href="https://demo.awaikenthemes.com/landing/firevall-html/" target="_blank">https://demo.awaikenthemes.com/landing/firevall-html/</a></li>
	<li>Created by: Awaiken</li>
	<li>Support email: <a href="mailto:&#115;&#117;&#112;&#112;&#111;&#114;&#116;&#64;&#97;&#119;&#97;&#105;&#107;&#101;&#110;&#46;&#99;&#111;&#109;?subject=Support%20for%20Applaunch%20html%20template">&#115;&#117;&#112;&#112;&#111;&#114;&#116;&#64;&#97;&#119;&#97;&#105;&#107;&#101;&#110;&#46;&#99;&#111;&#109;</a></li>
	</ul>
		</section>
	
	<section id="introdution">
	<div class="page-header"><h3>Introdution</h3><hr class="notop"></div>

	<div>&nbsp;</div>
<h4>Features</h4>
<ul class="features">
					<li>Modern, Flat & Unique Design.</li>
					<li>Bootstrap Grid Layout.</li>
					<li>Effects And Animations</li>
					<li>w3c valid html5 & css3</li>
					<li>Fully Responsive</li>
					<li>Cross Browser Compatible</li>
					<li>SEO Friendly</li>
					<li>Easy to Edit & Customize</li>
					<li>Working AJAX Contact Form</li>
					<li>Well Written Commented HTML5 & CSS3 code</li>
					<li>Detailed documentation</li>
					</ul>
</section>
<section id="folders_files">
	<div class="page-header"><h3>Folders & Files</h3><hr class="notop"></div>
<p>
	Template folders and files are divided as following.</p>
<ol>
	<li>css - contains css files</li>
	<li>webfonts - contains fonts files</li>
	<li>images - contains images</li>
	<li>js - contains javascript files</li>
	<li>index.html - Main landing page</li>
	<li>index-image.html - Hero Image Background</li>
	<li>index-video.html - Hero Video Background</li>
	<li>index-slider.html - Slideshow Layout</li>
	<li>about.html - About us page</li>
	<li>services.html - Services Page</li>
	<li>service-single.html - Service Single Page</li>
	<li>case-study.html - Case Study List Page</li>
	<li>case-study-single.html - Case Study Single Page</li>
	<li>blog.html - Blog Archive page</li>
	<li>blog-single.html - Blog Single page</li>
	<li>team.html - Team List page</li>
	<li>team-single.html - Team Details page</li>
	<li>pricing.html - Pricing Package page</li>
	<li>image-gallery.html - Gallery page</li>
	<li>video-gallery.html - Video Gallery page</li>
	<li>testimonial.html - Testimonials page</li>
	<li>contact.html - Contact us page</li>
	<li>faqs.html - FAQs page</li>
	<li>404.html - Page Not Found page</li>
	<li>form-process.php - contact form process file</li>
</ol>

</section>

<section id="css_files">
	<div class="page-header"><h3>CSS Files</h3><hr class="notop"></div>
<p> We have used following CSS files to create different layout.</p>
<ol>
	<li>bootstrap.min.css <span class="Apple-tab-span" style="white-space:pre"> </span>- Framework</li>
	<li>slicknav.min.css <span class="Apple-tab-span" style="white-space:pre"> </span>- Responsive Slick Menu</li>
	<li>swiper-bundle.min.css <span class="Apple-tab-span" style="white-space:pre"> </span>- Swiper carousel</li>
	<li>all.css <span class="Apple-tab-span" style="white-space:pre"> </span>- Font icons font-awesome</li>
	<li>animate.css <span class="Apple-tab-span" style="white-space:pre"> </span>- animated css</li>
	<li>mousecursor.css <span class="Apple-tab-span" style="white-space:pre"> </span>- Mouse cursor css</li>
	<li>magnific-popup.css <span class="Apple-tab-span" style="white-space:pre"> </span>- Magnific Popup css</li>
	<li>custom.css <span class="Apple-tab-span" style="white-space:pre"> </span>- Css file for layout</li>
</ol>
</section>

<section id="javascript">
	<div class="page-header"><h3>JavaScript</h3><hr class="notop"></div>
<p> We have used following Javascript files to create different layout.</p>
<ol>
	<li>jquery-3.7.1.min.js<span class="Apple-tab-span" style="white-space:pre"> </span>- Javascript library</li>
	<li>bootstrap.min.js <span class="Apple-tab-span" style="white-space:pre"> </span>- Bootstrap framework</li>
	<li>validator.min.js <span class="Apple-tab-span" style="white-space:pre"> </span>- Contact form validation</li>
	<li>jquery.slicknav.js <span class="Apple-tab-span" style="white-space:pre"> </span>- Responsive Menu</li>
	<li>swiper-bundle.min.js<span class="Apple-tab-span" style="white-space:pre"> </span>- Swiper carousel</li>
	<li>jquery.waypoints.min.js<span class="Apple-tab-span" style="white-space:pre"> </span>-  Trigger a function when you scroll to an element</li>
	<li>jquery.counterup.min.js<span class="Apple-tab-span" style="white-space:pre"> </span>- Animates a number from zero (counting up towards it)</li>
	<li>jquery.magnific-popup.min.js<span class="Apple-tab-span" style="white-space:pre"> </span>- Magnific Popup</li>
	<li>parallaxie.js <span class="Apple-tab-span" style="white-space:pre"> </span>- Parallax Background</li>
	<li>SmoothScroll.js <span class="Apple-tab-span" style="white-space:pre"> </span>- SmoothScroll</li>
	<li>SplitText.js<span class="Apple-tab-span" style="white-space:pre"> </span>- Split Text</li>
	<li>gsap.min.js<span class="Apple-tab-span" style="white-space:pre"> </span>- GreenSock Library</li>
	<li>magiccursor.js<span class="Apple-tab-span" style="white-space:pre"> </span>- Magic Animated Cursor</li>
	<li>ScrollTrigger.min.js <span class="Apple-tab-span" style="white-space:pre"> </span>- Trigger a Text Effect</li>
	<li>jquery.mb.YTPlayer.min.js <span class="Apple-tab-span" style="white-space:pre"> </span>- Youtube video background</li>
	<li>wow.js<span class="Apple-tab-span" style="white-space:pre"> </span>- Animation </li>
	<li>function.js<span class="Apple-tab-span" style="white-space:pre"> </span>- configuration code for layout</li>
</ol>
<b>Note:</b> We have included only required file in particular layout.
</section>

<section id="video">
	<div class="page-header"><h3>Hero section video background</h3><hr class="notop"></div>
	<div>index-video.html - Default is self hosted video background.</div>
	
	<p>
	To use Youtube video open the file <code>index-video.html</code> in editor after that remove the <strong>selfhosted code</strong> and uncomment the <strong>Youtube code</strong> See the below screenshot  <br><br>
	<img width="100%" src="assets/images/video-bg.jpg" >
	</p>
</section>

<section id="php">
	<div class="page-header"><h3>Contact form PHP</h3><hr class="notop"></div>
	<div>form-process.php - is used for sending an email&nbsp;</div>
	<div>&nbsp;</div>
	
	<div>You need to add your email, please open "form-process.php" file and edit variable $EmailTo.</div>
	<pre class="prettyprint php">$EmailTo = &quot;<EMAIL>&quot;;</pre>
</section>

<section id="icons">
	<div class="page-header"><h3>Icons</h3> <hr class="notop"></div>
	<p>font library</p>
	<ol>
		<li><b>fontawesome</b> : Here you can see icon cheatsheet <a rel="noreferrer noopener" target="_blank" href="https://fontawesome.com/search" target="_blank">https://fontawesome.com/search</a></li>
	</ol>
	
</section>

<section id="sources_and_credits">
	<div class="page-header"><h3>Sources and Credits</h3><hr class="notop"></div>
	
<div>Twitter Bootstrap framework:</div>
<ul>
	<li><a rel="noreferrer noopener" target="_blank" href="http://getbootstrap.com/">http://getbootstrap.com/</a></li>
</ul>
<div>&nbsp;</div>
<div>&nbsp;</div>
<div>Icons:</div>
<ul>
	<li><a rel="noreferrer noopener" target="_blank" href="https://fontawesome.com/">https://fontawesome.com/</a></li>
</ul>
<div>&nbsp;</div>
<div>&nbsp;</div>
<div>Images in preview are used from:</div>
<ul>
	<li><a rel="noreferrer noopener" target="_blank" href="http://www.freepik.com/">http://www.freepik.com/</a></li>
</ul>
<div>&nbsp;</div>
<div>
	Note : The images used in the template are not included in the main download file, they are only for the preview purpose.
</div>
</section>


	</div>
</body>
</html>